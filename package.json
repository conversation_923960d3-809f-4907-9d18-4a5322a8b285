{"name": "figma-ui-generator", "version": "0.1.0", "private": true, "scripts": {"figma": "pnpm -F @abc-figma/figma-ui-generator dev", "figma:preview": "pnpm -F @abc-figma/figma-ui-generator preview", "ui-transfer": "pnpm -F @abc-figma/ui-transfer dev", "figma:build": "pnpm --filter @abc-figma/figma-ui-generator... run build", "ui": "pnpm -F @abc-figma/vue-playground serve", "doc-gen": "pnpm -F @abc-figma/figma-ui-generator doc-gen", "config:build": "pnpm -F @abc-figma/ui-transfer build"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "dependencies": {"dotenv": "^16.4.7", "http-server": "^14.1.1", "sortablejs": "^1.15.6"}}