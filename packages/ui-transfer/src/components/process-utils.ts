import { componentConfigs, defaultComponentMapping } from "./config";
import {
    Abc<PERSON><PERSON>omponent,
    ConversionContext, GlobalContext,
    LayoutInfo,
    NodeContext,
    ResponsiveType
} from "./types";
import deepmerge from "deepmerge";
import {BaseNode} from "@figma/plugin-typings/plugin-api-standalone";
import {toKebabCase} from "@/utils/utils";

export async function convertNode(nodeContext: NodeContext, globalContext: GlobalContext): Promise<AbcUIComponent | null> {
    const { node } = nodeContext;

    const visible = node.visible ?? true;
    if (!visible) {
        return null;
    }

    // 更新统计信息
    if (globalContext.stats) {
        globalContext.stats.totalNodes++;
    }

    // 初始化转换上下文
    let context: ConversionContext = {
        componentType: getComponentType(node),
        name: node.name,
        metadata: {
            transformationLog: [],
        }
    };

    // 更新节点上下文
    nodeContext.context = context;

    const config = componentConfigs[context.componentType];

    console.log('node', node.name, context.componentType, config);

    if (config) {
        globalContext.suggestedComponents.add(config.type);
        context.metadata.transformationLog.push(`识别到组件类型: ${config.type}`);
    }

    // 提取组件属性
    const componentProperties = await extractComponentProperties(node);
    if (componentProperties) {
        context.componentProperties = componentProperties;
        context.metadata.transformationLog.push('提取到组件属性');
    }
    // 处理布局信息
    // if (config?.isContainer) {
    //     context.layout = extractLayout(node);
    //     context.metadata.transformationLog.push('分析容器布局');
    // }

    // 处理子节点
    if ('children' in node) {
        const container = node as FrameNode | GroupNode | InstanceNode | ComponentNode;

        if ((config && (!config.isContainer || config.customChildrenProcess)) || nodeContext.customChildrenProcess) {
            // 如果是非容器组件或者需要自定义处理，交给 customTransformer 处理
            context.metadata.transformationLog.push('子节点将由自定义转换器处理');
        } else {
            // 如果是容器组件，递归转换子元素
            const childResults = [];
            for (const child of container.children) {
                // 创建子节点上下文
                const childNodeContext: NodeContext = {
                    node: child,
                    parent: nodeContext
                };
                const converted = await convertNode(childNodeContext, globalContext);
                if (converted) {
                    // 如果当前节点是 flex，处理下 children 的样式
                    // if (context.componentType === 'AbcFlex') {
                    //     if (child.layoutSizingHorizontal === 'FILL') {
                    //         converted.style = {
                    //             ...converted.style,
                    //             flex: '1'
                    //         }
                    //     }
                    // }
                    childResults.push(converted);
                }
            }
            if (childResults.length > 0) {
                context.children = childResults;
                context.metadata.transformationLog.push(`处理了 ${childResults.length} 个子节点`);
            }
        }
    }

    // 处理文本节点
    if (node.type === 'TEXT') {
        const textContent = extractTextContent(node);
        if (textContent) {
            context.componentProperties = {
                ...context.componentProperties,
                text: textContent
            };
            context.metadata.transformationLog.push('提取文本内容');
        }
    }
    // console.log('nodeContext', nodeContext, config);

    const isBizComponent = node.name.startsWith('Biz');
    if(isBizComponent){
        context.componentGuideline = [`
            在生成的vue sfc 代码中需要对${node.name}组件进行导入与局部注册, 注意只对Biz开头的业务组件做处理，Abc开头的基础组件不需要处理。
            导入格式如下：import ${node.name} from '@/components-composite/${toKebabCase(node.name)}/index.js';
            注册格式如下：components: { ${node.name} }
            在vue template中使用时组件名使用小驼峰${toKebabCase(node.name)}
         `]
    }
    // 执行自定义转换
    if (config?.customTransformer) {
        try {
            const customUpdates = await config.customTransformer(nodeContext, globalContext);
            // Object.assign(context, customUpdates);
            context = deepmerge(context, customUpdates);
            context.metadata.transformationLog.push('应用自定义转换规则');
        } catch (error: any) {
            console.error('自定义转换器错误:', error, node, context.metadata.transformationLog);
            context.metadata.transformationLog.push(`自定义转换器错误: ${error.message}`);
            if (globalContext.stats) {
                globalContext.stats.failedNodes++;
            }
            return null;
        }
    }

    // 更新统计信息
    if (globalContext.stats) {
        globalContext.stats.convertedNodes++;
    }

    const result = {
        ...context,
    };

    delete result.metadata;

    return { ...result };
}

// 获取组件类型
export function getComponentType(node: BaseNode): string {
    // 首先检查节点名称是否匹配任何已配置的组件
    // 1. 先尝试精确匹配
    let matchedConfig = Object.keys(componentConfigs).find(key => {
        const nodeName = node.name.toLowerCase();
        const configKey = key.toLowerCase();
        return nodeName === configKey || new RegExp(`^${configKey}\d+$`).test(nodeName)
    });


    // 特殊处理一些组件
    if (node.type === 'INSTANCE') {
        const nodeLowerName = node.name.toLowerCase();
        if (nodeLowerName.includes('abcfileview') || nodeLowerName.includes('file view')) {
            return 'AbcFileViewV2';
        }
        if (nodeLowerName.includes('tabs-horizontal')) {
            return 'AbcTabsV2';
        }
        if (nodeLowerName.includes('abctag')) {
            return 'AbcTagV2';
        }
        if (nodeLowerName.includes('abctree')) {
            return 'AbcTreeV2';
        }
        if (nodeLowerName.includes('abctipscard')) {
            return 'AbcTipsCardV2';
        }
        if (nodeLowerName.includes('abctransfer')) {
            return 'AbcTransferV2';
        }

        if (nodeLowerName.startsWith('s-')) {
            return 'AbcIcon';
        }
        if (nodeLowerName === 'abclistitem') {
            return 'AbcListItem';
        }

        if (nodeLowerName === 'abcdatepicker' && node.componentProperties.type.value === 'Time') {
            return 'AbcTimePicker';
        }
        if (nodeLowerName === 'abcdatepicker' && node.componentProperties.type.value === 'YearMonthDayTime') {
            return 'AbcDateTimePicker';
        }
    }

    // 2. 如果没有精确匹配到，再使用 includes 匹配
    if (!matchedConfig) {
        matchedConfig = Object.keys(componentConfigs).find(key =>
            node.name.toLowerCase().includes(key.toLowerCase())
        );
    }

    if (matchedConfig) {
        return componentConfigs[matchedConfig].type;
    }

    // 没有匹配中组件，先看看是否是 autoLayout
    if (isAutoLayout(node)) {
        return isAbcSpace(node) ? 'AbcSpace' : 'AbcFlex';
    }

    // 否则使用默认映射
    return defaultComponentMapping[node.type] || node.type;
}

// Helper function to analyze layout type
export function extractLayout(node: BaseNode): LayoutInfo {
    const layout: LayoutInfo = {
        type: 'normal',
        responsive: {
            widthType: 'fixed',
            heightType: 'fixed'
        }
    };

    // 分析布局类型
    if (node.type === 'FRAME') {
        if (node.layoutMode === 'HORIZONTAL' || node.layoutMode === 'VERTICAL') {
            layout.type = 'flex';
            layout.direction = node.layoutMode === 'HORIZONTAL' ? 'horizontal' : 'vertical';
            layout.gap = node.itemSpacing;

            // 分析对齐方式
            layout.alignment = {
                horizontal: convertFigmaToFlexAlignment(node.primaryAxisAlignItems),
                vertical: convertFigmaToFlexAlignment(node.counterAxisAlignItems)
            };
        } else if (node.layoutMode === 'NONE' && node.layoutGrids && node.layoutGrids.length > 0) {
            layout.type = 'grid';
            const columnsGrid = node.layoutGrids.find(grid => grid.pattern === 'COLUMNS') as RowsColsLayoutGrid;
            const rowsGrid = node.layoutGrids.find(grid => grid.pattern === 'ROWS') as RowsColsLayoutGrid;

            layout.gridInfo = {
                columns: columnsGrid?.count || 1,
                columnGap: columnsGrid?.gutterSize || 0,
                rowGap: rowsGrid?.gutterSize || 0
            };
        }
    }

    // 分析间距
    // if ('paddingLeft' in node) {
    //   layout.spacing = {
    //     padding: {
    //       top: node.paddingTop,
    //       right: node.paddingRight,
    //       bottom: node.paddingBottom,
    //       left: node.paddingLeft
    //     }
    //   };
    // }

    // 分析响应式信息
    layout.responsive = analyzeResponsiveLayout(node);

    return layout;
}

export function analyzeResponsiveLayout(node: BaseNode): LayoutInfo['responsive'] {
    const responsive = {
        widthType: 'fixed' as ResponsiveType,
        heightType: 'fixed' as ResponsiveType
    };

    if ('constraints' in node) {
        // 水平约束
        switch (node.constraints.horizontal) {
            case 'STRETCH':
                responsive.widthType = 'fluid';
                break;
            case 'SCALE':
                responsive.widthType = 'auto';
                break;
        }

        // 垂直约束
        switch (node.constraints.vertical) {
            case 'STRETCH':
                responsive.heightType = 'fluid';
                break;
            case 'SCALE':
                responsive.heightType = 'auto';
                break;
        }
    }

    return responsive;
}

export function convertFigmaToFlexAlignment(
    alignment: string
): 'start' | 'center' | 'end' | 'space-between' {
    const alignmentMap: Record<string, 'start' | 'center' | 'end' | 'space-between'> = {
        'MIN': 'start',
        'CENTER': 'center',
        'MAX': 'end',
        'SPACE_BETWEEN': 'space-between'
    };
    return alignmentMap[alignment] || 'start';
}

export function isIconProperty(key: string) {
    return key.includes('ic-name');
}

export function getIconPropertyName(key: string) {
    if (!isIconProperty(key)) {
        return key;
    }
    return key.split('#')[0];
}

export async function getIconName(value: { type: string; value: string }) {
    // if (value && value.type === 'INSTANCE_SWAP') {
    //     if(figma) {
    //         const iconNode = await figma.getNodeByIdAsync(value.value);
    //         if (iconNode) {
    //             return iconNode.name;
    //         }
    //     }
    // }
    console.warn('getIconName failed, use value directly', value);
    return value.value;
}

export function isReferenceProperty(key: string) {
    return key.includes('#');
}

export function getReferencePropertyName(key: string) {
    if (!isReferenceProperty(key)) {
        return key;
    }
    return key.split('#')[0];
}

/**
 * 通用的提取组件属性函数
 * 1. 处理 icon，ic-name- 开头的属性
 * 2. 处理 reference，包含 # 的属性
 * @param node
 * @returns
 */
export async function extractComponentProperties(node: BaseNode): Promise<Record<string, any> | null> {
    if ('componentProperties' in node) {
        const properties: Record<string, any> = {};
        const componentProps = (node as InstanceNode).componentProperties || {};

        for (const [key, value] of Object.entries(componentProps)) {
            // console.log('extractComponentProperties', key, value);
            if (isIconProperty(key)) {
                const iconKey = getIconPropertyName(key);
                properties[iconKey] = await getIconName(value);
            } else if (isReferenceProperty(key)) {
                const refKey = getReferencePropertyName(key);
                properties[refKey] = value.value;
            } else {
                properties[key] = value.value;
            }
        }
        if (Object.keys(properties).length > 0) {
            return properties;
        }
    }
    return null;
}

export function extractTextContent(node: BaseNode): string | null {
    if(node.name.toLowerCase()==="abctext"){
        return extractTextContent(node.children?.[0]);
    }
    if ('characters' in node) {
        return node.characters;
    }
    return null;
}

// 判断节点是否为 Auto Layout
export function isAutoLayout(node: BaseNode): node is FrameNode | ComponentNode {
    return (
        (node.type === 'FRAME' || node.type === 'COMPONENT' || node.type === 'INSTANCE') &&
        'layoutMode' in node &&
        (node.layoutMode === 'HORIZONTAL' || node.layoutMode === 'VERTICAL')
    );
}

/**
 * 判断节点是否为 AbcSpace
 * 1. type 为 FRAME
 * 2. layoutMode 为 HORIZONTAL
 * 3. layoutSizingHorizontal 不为 FILL
 * @param node
 * @returns
 */
export function isAbcSpace(node: BaseNode): node is FrameNode | ComponentNode {
    return (
        (node.type === 'FRAME' || node.type === 'COMPONENT') &&
        'layoutMode' in node &&
        (node.layoutMode === 'HORIZONTAL') &&
        node.layoutSizingHorizontal !== 'FILL'
    );
}

/**
 * 判断节点是否为 AbcFlex
 * AutoLayout 但不是 AbcSpace
 * @param node
 * @returns
 */
export function isAbcFlex(node: BaseNode): node is FrameNode | ComponentNode {
    return isAutoLayout(node) && !isAbcSpace(node);
}

export function extractFlexInfo(node: FrameNode | ComponentNode): Record<string, any> {
    // Flexbox 属性映射
    const flexDirection = node.layoutMode === 'HORIZONTAL' ? 'row' : 'column';

    const justifyContentMap: Record<'MIN' | 'CENTER' | 'MAX' | 'SPACE_BETWEEN', string> = {
        MIN: 'flex-start',
        CENTER: 'center',
        MAX: 'flex-end',
        SPACE_BETWEEN: 'space-between',
    };

    const alignItemsMap: Record<'MIN' | 'CENTER' | 'MAX', string> = {
        MIN: 'flex-start',
        CENTER: 'center',
        MAX: 'flex-end',
    };

    const gap = node.itemSpacing;

    return {
        flexDirection,
        justifyContent: justifyContentMap[node.primaryAxisAlignItems] || 'flex-start',
        alignItems: alignItemsMap[node.counterAxisAlignItems] || 'stretch',
        gap,
    };
}

export function convertToSpace(node: FrameNode | ComponentNode): Record<string, any> {
    const flexInfo = extractFlexInfo(node);

    const direction = flexInfo.flexDirection === 'row' ? 'horizontal' : 'vertical';

    const sizeEnums = {
        8: 'small',
        16: 'middle',
        24: 'large',
    }

    const size = sizeEnums[flexInfo.gap] ? sizeEnums[flexInfo.gap] : `${flexInfo.gap}`;

    return {
        direction,
        size,
    };
}

// 将 Auto Layout 转换为 Flex 布局信息
export function convertToFlex(node: FrameNode | ComponentNode): Record<string, any> {
    if (!isAutoLayout(node)) {
        throw new Error('Node is not an Auto Layout.');
    }

    const flexInfo = extractFlexInfo(node);

    const gapEnums = {
        4: 'small',
        8: 'middle',
        16: 'large',
    }

    const gap = gapEnums[flexInfo.gap] ? gapEnums[flexInfo.gap] : `${flexInfo.gap}`;

    let vertical = undefined;
    if (flexInfo.flexDirection === 'column') {
        vertical = true;
    }

    return {
        vertical,
        justify: flexInfo.justifyContent,
        align: flexInfo.alignItems,
        gap,
    };
}

// 判断子项是否只有一个 icon（特指设计图不是用的 AbcIcon,而是直接一个图标s-前缀的）
export function isSingleSubIcon(node: BaseNode): boolean {
    if (node?.children?.length === 1 && node.children[0].name?.startsWith('s-')) {
        return true;
    }
    return false;
}
