import {
    ComponentConfig,
    ComponentSlot, GlobalContext,
    NodeContext,
} from "@/components/types";
import {convertNode, extractComponentProperties} from "@/components/process-utils";

export const bizQuickOptionsPanelConfig: ComponentConfig = {
    type: "BizCustomizationOptions",
    isContainer: false,
    customTransformer: async (
        nodeContext: NodeContext,
        globalContext: GlobalContext,
    ) => {
        const result: Record<string, any> = {
            componentSlot: <ComponentSlot[]>[],
            componentGuideline: [],
        };

        const { context, node } = nodeContext;

        const { children } = (node || {}) as any;
        const bodyNode = (children || []).find((child: any) => child.name === 'body');
        const { children: bodyChildren } = bodyNode || {};

        if (!bodyChildren || bodyChildren.length === 0) {
            return result;
        }

        const grandChildren = bodyChildren[0];

        const componentProperties = await extractComponentProperties(grandChildren);

        if (context) {
            context.componentProperties = componentProperties as Record<string, any>;
        }

        if (componentProperties) {
            if (componentProperties.disabledDelete === 'false' || componentProperties.disabledDelete === false) {
                delete componentProperties.disabledDelete;
            } else if (componentProperties.disabledDelete === 'true' || componentProperties.disabledDelete === true) {
                componentProperties.disabledDelete = true;
            }
            if (componentProperties.icon === 'false' || componentProperties.icon === false) {
                delete componentProperties.icon;
            } else if (componentProperties.icon === 'true' || componentProperties.icon === true) {
                componentProperties.showIcon = true;
            }
            if (componentProperties.sortable === 'true' || componentProperties.sortable === true) {
                delete componentProperties.sortable;
            } else if (componentProperties.sortable === 'false' || componentProperties.sortable === false) {
                componentProperties.sortable = false;
            }
        }

        const grandChildrenPropertiesList = [];
        for (const grandChild of bodyChildren) {
            grandChildrenPropertiesList.push(await extractComponentProperties(grandChild));
        }
        const editorGrandChildIndex = grandChildrenPropertiesList.findIndex((properties: any) => properties.editor);
        if (editorGrandChildIndex > -1) {
            const editorGrandChild = bodyChildren[editorGrandChildIndex];

            result.componentGuideline.push('使用 BizCustomizationOptions 的 editor 插槽');
            if (editorGrandChild.children && editorGrandChild.children.length > 0) {
                const editorSlotNode = editorGrandChild.children.find((x: any) => x.name === 'editor');
                if (editorSlotNode) {
                    const editorNodeContext = {
                        node: editorSlotNode,
                    };
                    const editorNode = await convertNode(editorNodeContext, globalContext);
                    result.componentSlot.push({
                        name: 'editor',
                        content: editorNode,
                    });
                }
            }
        }

        if (context && context.componentProperties) {
            delete context.componentProperties.states;
            delete context.componentProperties.editor;
            delete context.componentProperties.content;
        }

        // 如果没有指南，删除该属性
        if (!(result.componentGuideline as string[]).length) {
            delete result.componentGuideline;
        }

        // 如果没有插槽，删除该属性
        if (!(result.componentSlot as ComponentSlot[]).length) {
            delete result.componentSlot;
        }

        return result;
    },
};
