import {ComponentConfig, NodeContext} from '../types';

export const checkboxButtonConfig: ComponentConfig = {
    type: 'AbcCheckboxButton',
    isContainer: false,
    customTransformer: async (nodeContext: NodeContext) => {
        const result: Record<string, any> = {};

        const {context} = nodeContext;

        const {
            componentProperties,
        } = context!;

        if (componentProperties) {
            console.log('abcComponentProperties ==> ', componentProperties);

            if (componentProperties.states === 'disable') {
                componentProperties.disabled = true;
            }
            if (componentProperties.disabled === 'false' || componentProperties.disabled === false) {
                delete componentProperties.disabled;
            }
            if (componentProperties.variant === 'default') {
                delete componentProperties.variant;
            }
            if (componentProperties.theme === 'default') {
                delete componentProperties.theme;
            }
            // 删除不需要的属性
            delete componentProperties.states;
            delete componentProperties.upcoming;
        }


        return result;
    }
};
