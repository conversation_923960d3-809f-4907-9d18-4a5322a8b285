import { convertNode } from '../process-utils';
import { ComponentConfig, ComponentSlot, GlobalContext, NodeContext } from '../types';

export const radioGroupConfig: ComponentConfig = {
  type: 'AbcRadioGroup',
  isContainer: true,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const { context, node } = nodeContext;
    
    if (!context || !context.componentProperties) {
      return {};
    }
    
    const { componentProperties } = context;
    const result = {
      componentSlot: <ComponentSlot[]>[],
    };
    
    // itemBlock 处理 - 将 itemBlock 单元格设置为一行
    if (componentProperties?.['itemBlock'] === 'True') {
      componentProperties.itemBlock = true;
    } else {
      componentProperties.itemBlock = false;
    }
    
    // 处理子组件
    if ('children' in node && node.children?.length) {
      // 使用 for...of 循环遍历子组件
      for (const child of node.children) {
        if (child.name) {
          // 构造子组件的 NodeContext
          const childNodeContext = {
            node: child,
          };
          
          // 使用 convertNode 转换子组件
          const childNode = await convertNode(childNodeContext, globalContext);
          
          // 将转换后的子组件添加到 componentSlot 中
          result.componentSlot.push({
            name: 'default',
            content: childNode,
          });
        }
      }
    }
    
    // 如果没有子组件，删除 componentSlot 属性
    if (!result.componentSlot.length) {
      delete result.componentSlot;
    }
    
    return result;
  }
};
