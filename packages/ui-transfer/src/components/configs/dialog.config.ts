import {convertNode} from '../process-utils';
import {ComponentConfig, GlobalContext, NodeContext} from '../types';

// 辅助函数：安全获取属性值
function safeGetProperty(obj: any, key: string, defaultValue: any = undefined): any {
    try {
        return obj && typeof obj === 'object' && key in obj ? obj[key] : defaultValue;
    } catch (error) {
        console.warn(`Error accessing property ${key}:`, error);
        return defaultValue;
    }
}

// 辅助函数：安全的布尔值转换
function safeBooleanConvert(value: any, defaultValue: boolean = false): boolean {
    if (value === null || value === undefined) {
        return defaultValue;
    }
    if (typeof value === 'boolean') {
        return value;
    }
    if (typeof value === 'string') {
        const lowerValue = value.toLowerCase().trim();
        return lowerValue === 'true' || lowerValue === '1';
    }
    return Boolean(value);
}

// 辅助函数：验证尺寸值
function validateSize(size: any, validSizes: string[], defaultSize: string = 'default'): string {
    if (!size || typeof size !== 'string') {
        return defaultSize;
    }
    return validSizes.includes(size) ? size : defaultSize;
}

export const dialogConfig: ComponentConfig = {
    type: 'AbcDialog',
    isContainer: true,
    customChildrenProcess: true,
    nodeWhitelist: ['top-extend', 'title-append', 'search', 'header', 'content', 'default', 'body', 'footer'],
    customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
        const result: Record<string, any> = {
            componentProperties: {},
            componentSlot: [],
            componentGuideline: [],
        };

        // 安全检查输入参数
        if (!nodeContext || !nodeContext.node) {
            console.error('Invalid nodeContext or missing node');
            return result;
        }

        const node = nodeContext.node;
        const context = nodeContext.context;

        // 安全检查 context
        if (!context) {
            console.warn('Missing context in nodeContext');
            return result;
        }

        const frameNode = node as InstanceNode;

        // 安全获取 componentProperties
        const componentProperties = safeGetProperty(context, 'componentProperties', {});

        // 确保 componentProperties 是一个有效对象
        if (componentProperties && typeof componentProperties === 'object') {
            const sizeArray = ['small', 'medium', 'default', 'large', 'xlarge', 'huge', 'hugely'];

            // 安全处理 size 属性
            const currentSize = safeGetProperty(componentProperties, 'size');
            const validatedSize = validateSize(currentSize, sizeArray, 'default');
            result.componentProperties.size = validatedSize;

            console.log('dialog componentProperties', context, componentProperties);

            // 安全处理布尔值属性
            const booleanProperties = [
                'showHeaderBorderBottom',
                'showClose',
                'fullscreen',
                'responsive'
            ];

            booleanProperties.forEach(propName => {
                const propValue = safeGetProperty(componentProperties, propName);
                if (propValue !== undefined) {
                    result.componentProperties[propName] = safeBooleanConvert(propValue);
                }
            });

            // 安全处理 topExtend 属性
            const topExtendValue = safeGetProperty(componentProperties, 'topExtend');

            if (safeBooleanConvert(topExtendValue)) {
                try {
                    // 安全检查 frameNode 和其 children
                    if (frameNode && 'children' in frameNode && Array.isArray(frameNode.children)) {
                        const topExtendNode = frameNode.children.find(child =>
                            child && child.name && child.name.toLowerCase() === 'top-extend'
                        );

                        if (topExtendNode) {
                            const topExtendNodeContext = {
                                node: topExtendNode,
                            };
                            const topExtendInfo = await convertNode(topExtendNodeContext, globalContext);

                            let finalTopExtendInfo = topExtendInfo;
                            if (topExtendInfo?.componentSlot?.[0]?.content?.length && topExtendInfo.componentSlot[0].name === 'default') {
                                finalTopExtendInfo = topExtendInfo.componentSlot[0].content[0];
                            }
                            if (finalTopExtendInfo) {
                                result.componentSlot.push({
                                    slotName: 'top-extend',
                                    figmaInfo: finalTopExtendInfo
                                });
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error processing topExtend:', error);
                }
            }

            // 安全清理不需要的属性
            const propsToDelete = ['customTop', 'topExtend'];
            propsToDelete.forEach(prop => {
                if (prop in componentProperties) {
                    delete componentProperties[prop];
                }
            });

            // 安全处理 footer 属性
            const footerValue = safeGetProperty(componentProperties, 'footer');
            if (footerValue === 'false' || footerValue === false) {
                delete componentProperties.footer;
            }

            // 安全查找子节点
            let bodyNode, footerNode, searchNode, defaultNode;

            if (frameNode && 'children' in frameNode && Array.isArray(frameNode.children)) {
                try {
                    bodyNode = frameNode.children.find(child =>
                        child && child.name && child.name.toLowerCase() === 'body'
                    );
                    footerNode = frameNode.children.find(child =>
                        child && child.name && child.name.toLowerCase() === 'footer'
                    );
                    searchNode = frameNode.children.find(child =>
                        child && child.name && child.name.toLowerCase() === 'search'
                    );
                    defaultNode = frameNode.children.find(child =>
                        child && child.name && child.name.toLowerCase() === 'content'
                    );
                } catch (error) {
                    console.error('Error finding child nodes:', error);
                }
            } else {
                console.warn('frameNode children not available for node searching');
            }

            // 安全创建节点上下文的辅助函数
            function createSafeNodeContext(node: any): NodeContext | null {
                if (!node) {
                    return null;
                }
                return {
                    node: node as SceneNode,
                    context: nodeContext.context
                };
            }

            const footerNodeContext: any = createSafeNodeContext(footerNode);
            const bodyNodeContext: any = createSafeNodeContext(bodyNode);
            const searchNodeContext: any = createSafeNodeContext(searchNode);
            const defaultNodeContext: any = createSafeNodeContext(defaultNode);

            // 处理 slot
            const headerNode = frameNode.children.find(child => child.name.toLowerCase() === 'title');
            console.log('dialog的title', headerNode)
            const headerNodeContext: NodeContext = {
                node: headerNode as SceneNode,
            };
            if (headerNode) {
                // 获取 preset 属性
                const preset = (headerNode as InstanceNode).componentProperties?.preset?.value;
                console.log('dialog的title preset', preset);

                if (preset === 'default') {
                    // 查找 title 元素并获取其文本内容
                    const titleNode = (headerNode as FrameNode).children?.find((child: SceneNode) => child.name === 'title');
                    if (titleNode && 'characters' in titleNode) {
                        componentProperties.title = titleNode.characters;
                    }
                } else if (preset === 'titleDescription') {
                    // 遍历 children 找到 title 和 description
                    const children = (headerNode as FrameNode).children;
                    if (children) {
                        const titleNode = children.find((child: SceneNode) => child.name === 'title');
                        const descriptionNode = children.find((child: SceneNode) => child.name === 'description');

                        if (titleNode && 'characters' in titleNode) {
                            componentProperties.title = titleNode.characters;
                        }

                        if (descriptionNode) {
                            const descriptionContext: NodeContext = {
                                node: descriptionNode,
                            };
                            const descriptionInfo = await convertNode(descriptionContext, globalContext);
                            if (descriptionInfo) {
                                result.componentSlot.push({
                                    slotName: 'title-append',
                                    figmaInfo: descriptionInfo
                                });
                            }
                        }
                    }
                }
                // 超过两个元素，认为是自定义的
                else if ((headerNode as FrameNode).children?.length > 2) {
                    const headerSlotInfo = await convertNode(headerNodeContext, globalContext);
                    if (headerSlotInfo) {
                        result.componentSlot.push({
                            slotName: 'header',
                            figmaInfo: headerSlotInfo
                        });
                    }
                } else {
                    result.componentGuideline.push('从截图中分析弹窗标题，并给 AbcDialog 的 title 属性设置')
                }
            }

            if (footerNode) {
                const footerSlotInfo: any = await convertNode(footerNodeContext, globalContext);
                if (footerSlotInfo) {
                    console.log('dialogConfig footerSlotInfo', footerSlotInfo);
                    const {componentProperties} = footerSlotInfo;
                    const leftButton = componentProperties?.['left button'] || componentProperties?.actionStart;
                    const rightButton = componentProperties?.['right button'] || componentProperties?.actionEnd;
                    if (leftButton && rightButton) {
                        // 同时有两个按钮区域，使用 flex 布局，
                        footerSlotInfo.componentType = 'AbcFlex';
                        footerSlotInfo.componentProperties = {
                            justify: 'space-between'
                        };
                    } else if (rightButton) {
                        footerSlotInfo.componentType = 'AbcFlex';
                        footerSlotInfo.componentProperties = {
                            justify: 'flex-end'
                        };
                    } else if (leftButton) {
                        footerSlotInfo.componentType = 'AbcFlex';
                        footerSlotInfo.componentProperties = {
                            justify: 'flex-start'
                        };
                    }
                    let actionStartFigmaInfo;
                    let actionEndFigmaInfo;
                    
                    // 根据 actionStart 和 actionEnd 的值过滤 componentSlot 中的 content
                    if (footerSlotInfo.componentSlot && 
                        Array.isArray(footerSlotInfo.componentSlot) && 
                        footerSlotInfo.componentSlot[0]?.content && 
                        Array.isArray(footerSlotInfo.componentSlot[0].content)) {
                        
                        // 找到 actionStart 和 actionEnd 的figmaInfo
                        actionStartFigmaInfo = footerSlotInfo.componentSlot[0].content.find((item: any) => item.name === 'actionStart');
                        actionEndFigmaInfo = footerSlotInfo.componentSlot[0].content.find((item: any) => item.name === 'actionEnd');
                        
                        footerSlotInfo.componentSlot[0].content = footerSlotInfo.componentSlot[0].content.filter((item: any) => {
                            // 如果是 actionStart，根据 actionStart 的值决定是否保留
                            if (item.name === 'actionStart') {
                                return !!componentProperties?.actionStart;
                            }
                            // 如果是 actionEnd，根据 actionEnd 的值决定是否保留
                            if (item.name === 'actionEnd') {
                                return !!componentProperties?.actionEnd;
                            }
                            // 其他项保留
                            return true;
                        });
                    }

                    let finalFigmaInfo = footerSlotInfo;
                    if (componentProperties?.actionStart && !componentProperties?.actionEnd) {
                        finalFigmaInfo = actionStartFigmaInfo;
                    } else if (!componentProperties?.actionStart && componentProperties?.actionEnd) {
                        finalFigmaInfo = actionEndFigmaInfo;
                    }
                    result.componentSlot.push({
                        slotName: 'footer',
                        figmaInfo: finalFigmaInfo
                    });
                }
            }

            if (bodyNode) {
                const bodySlotInfo = await convertNode(bodyNodeContext, globalContext);
                if (bodySlotInfo) {
                    result.componentSlot.push({
                        slotName: 'default',
                        figmaInfo: bodySlotInfo
                    });
                }
            }

            // 处理特殊插槽
            if (searchNode) {
                const searchSlotInfo = await convertNode(searchNodeContext, globalContext);
                if (searchSlotInfo) {
                    result.componentSlot.push({
                        slotName: 'search',
                        figmaInfo: searchSlotInfo
                    });
                }
            }

            if (defaultNode) {
                const defaultSlotInfo = await convertNode(defaultNodeContext, globalContext);
                let finalDefaultSlotInfo = defaultSlotInfo;
                if (defaultSlotInfo?.componentSlot?.[0]?.content?.length && defaultSlotInfo.componentSlot[0].name === 'default') {
                    finalDefaultSlotInfo = defaultSlotInfo.componentSlot[0].content[0];
                }
                if (defaultSlotInfo) {
                    result.componentSlot.push({
                        slotName: 'default',
                        figmaInfo: finalDefaultSlotInfo
                    });
                }
            }
        }
        result.componentGuideline.push('保证AbcDialog设置了 v-model，并且初始变量是true, 如果有关闭按钮，点击关闭按钮可以关闭弹窗');

        if (!result.componentGuideline.length) {
            delete result.componentGuideline;
        }

        console.log('dialogConfig result', result);
        return result;
    }
};
