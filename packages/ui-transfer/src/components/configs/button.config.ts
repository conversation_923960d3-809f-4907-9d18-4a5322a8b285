import { ComponentConfig, ComponentSlot, GlobalContext, NodeContext } from '../types';

export const buttonConfig: ComponentConfig = {
  type: 'AbcButton',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result = {
      componentSlot: <ComponentSlot[]>[],
    }
    const {context, node} = nodeContext;
    // console.log('buttonConfig customTransformer', { nodeContext, globalContext });

    const {
      componentProperties,
    } = context;

    if (componentProperties?.['states'] === 'disable') {
      componentProperties.disabled = true;
    }
    // 删除不需要的属性
    delete componentProperties?.['states'];

    // 处理 icon
    if (componentProperties?.['icon-position-left']) {
      componentProperties.icon = componentProperties['ic-name-left'];
      componentProperties['icon-position'] = 'left';

    } else if (componentProperties?.['icon-position-right']) {
      // 删除 icon
      componentProperties.icon = componentProperties['ic-name-right'];
      componentProperties['icon-position'] = 'right';
    }
    // 删除 only-icon
    if (componentProperties?.['only-icon']) {
       componentProperties.icon = componentProperties['ic-name'];
    }
    if (componentProperties?.['icon-position'] === 'false') {
      // 删除 icon
      delete componentProperties?.['ic-name'];
      delete componentProperties?.['icon'];
      delete componentProperties?.['icon-position'];
    }
    delete componentProperties?.['ic-name-right'];
    delete componentProperties?.['icon-position-right'];
    delete componentProperties?.['ic-name-left'];
    delete componentProperties?.['icon-position-left'];
    delete componentProperties?.['only-icon'];

    if (componentProperties?.['width'] === 'off') {
      delete componentProperties?.['width'];
    }

    // 处理 text，看有没有 count
    function processTextContent(text: string) {
    // 处理 text，看有没有 count
          const textList = text?.split('·');
          const count = textList?.[1];
          // 判断是否是数字
          if (count && !isNaN(Number(count))) {
            componentProperties.count = count;
            result.componentSlot.push({
              name: 'default',
              content: textList?.[0],
            })
          } else {
            // console.log('buttonConfig default', text);
            result.componentSlot.push({
              name: 'default',
              content: text,
            })
          }
    }

    let text = componentProperties?.['text'];
    delete componentProperties?.['text'];

    if (!text) {
      const {
        children
      } = node;
      // 处理 text
      if (children?.length) {
        // console.log('buttonConfig children', children);
        const textNode = children.find(child => child.type === 'TEXT');
        // console.log('button textNode', textNode);
        if (textNode) {
          text = textNode.characters;
        }
      }
    }
    if (text) {
      processTextContent(text);
    }
    return result;
  }
};
