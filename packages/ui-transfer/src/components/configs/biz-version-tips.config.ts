import {ComponentConfig, GlobalContext, NodeContext} from '../types';

export const BizVersionTips: ComponentConfig = {
  type: 'BizVersionTips',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
    };

    const {
      context,
      node,
    } = nodeContext;
    const {
      componentProperties
    } = context;

    if (componentProperties) {

      if (componentProperties.states === 'disabled') {
        result.componentProperties.disabled = true
      }

      if (componentProperties.button === 'true') {
        result.componentProperties.showButton = true
      }

      // 提取文本
      const groupNode = node.children?.find(item=>item.name==="Group 1");

      if(groupNode){
        const [abcTextNode, abcButtonNode] = groupNode.children;

        if(abcTextNode){
          const textNode = abcTextNode.children.find(child => child.type === 'TEXT');
          if (textNode) {
            result.componentProperties.text = textNode.characters;
          }
        }

        if(abcButtonNode){
          const textNode = abcButtonNode.children.find(child => child.type === 'TEXT');
          if (textNode) {
            result.componentProperties.btnText = textNode.characters;
          }
        }
      }

      // 删除不需要的属性
      delete componentProperties.states;
      delete componentProperties.button;

    }

    return result;
  }
};
