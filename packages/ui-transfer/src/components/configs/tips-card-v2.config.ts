import { convertNode } from '../process-utils';
import { ComponentConfig, NodeContext, GlobalContext } from '../types';

export const tipsCardConfig: ComponentConfig = {
  type: 'AbcTipsCardV2',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
      componentSlot: [],
    };

    const { context, node } = nodeContext;

    const {
      componentProperties,
    } = context!;

    if (componentProperties) {
      if (componentProperties.icon && componentProperties.customIcon) {
        const iconContentNode = node.children?.find(child => child.name === 'icon')?.children?.[0]
        if (iconContentNode.name) {
          componentProperties.customIcon  = {
            name: iconContentNode.name,
          };
        }
      }
      if (componentProperties.title && componentProperties['title-text']) {
        componentProperties.title = componentProperties['title-text'];
        delete componentProperties['title-text'];
      }
      if (componentProperties.description && componentProperties['description-text']) {
        result.componentSlot.push({
          name: 'default',
          content: componentProperties['description-text'],
        })
        delete componentProperties.description;
        delete componentProperties['description-text'];
      }
      
      if (componentProperties.operate && componentProperties['operate-slots']) {
        const operateNode = node.children?.find(child => child.name === 'operate')?.children?.[0]
        if (operateNode) {
          const operateNodeResult = await convertNode({ node: operateNode, parent: nodeContext }, globalContext);
          if (operateNodeResult) {
            result.componentSlot.push({
              name: 'operate',
              content: operateNodeResult
            })
          }
        }
        delete componentProperties.operate;
        delete componentProperties['operate-slots'];
      }
    }

    return result;
  }
};
