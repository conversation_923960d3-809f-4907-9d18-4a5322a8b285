import { ComponentConfig, ComponentSlot, GlobalContext, NodeContext } from '../types';

export const autocompleteConfig: ComponentConfig = {
  type: 'AbcAutocomplete',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result = {
      componentSlot: <ComponentSlot[]>[],
      componentGuideline: [],
    };
    const { context, node } = nodeContext;
    const { componentProperties } = context;

    if (componentProperties) {
      // 宽度设置
      if (componentProperties?.['width']) {
        componentProperties.width = Number(componentProperties.width);
      }

      // size 处理
      if (componentProperties?.['size']) {
        const validSizes = ['tiny', 'small', 'medium', 'large', 'huge'];
        if (!validSizes.includes(componentProperties['size'])) {
          delete componentProperties.size;
        }
      }

      // placeholder 处理
      if (!componentProperties?.['placeholder']) {
        delete componentProperties.placeholder;
      }

      // clearable
      if (componentProperties?.['clearable'] === 'False') {
        componentProperties.clearable = false;
      } else {
        componentProperties.clearable = true;
      }

      // disabled
      if (componentProperties?.['disabled'] === 'True') {
        componentProperties.disabled = true;
      } else {
        componentProperties.disabled = false;
      }

      // readOnly
      if (componentProperties?.['readOnly'] === 'True') {
        componentProperties.readOnly = true;
      } else {
        componentProperties.readOnly = false;
      }

      // selectOnly
      if (componentProperties?.['selectOnly'] === 'True') {
        componentProperties.selectOnly = true;
      } else {
        componentProperties.selectOnly = false;
      }

      // 删除 states 属性
      if (componentProperties?.['states'] === 'disable') {
        componentProperties.disabled = true;
      }
      delete componentProperties?.['states'];
      // 找到 suggestionPanel 这个子元素
      const suggestionPanel = node.children?.find(child => child.name === 'suggestionPanel');
      // 获取 header 子元素
      // const header = suggestionPanel?.children?.find(child => child.name === 'header');

      // 获取 itemGroup 子元素
      const itemGroup = suggestionPanel?.children?.find(child => child.name === 'ColumnGroup');
      // 获取 suggestionHeader 子元素
      const suggestionHeader = itemGroup.children?.[0]?.children?.find(child => child.name === 'suggestionHeader');

      // 获取 suggestionColumn 子元素
      const suggestionColumn = itemGroup.children?.[0]?.children?.find(child => child.name === 'suggestionItemGroup');

      // 获取 footer 子元素
      const footer = suggestionPanel?.children?.find(child => child.name === 'suggestionFixedFooter');

      console.log('autocomplete-itemGroup', itemGroup);
      console.log('autocomplete-suggestionHeader', suggestionHeader);
      console.log('autocomplete-suggestionColumn', suggestionColumn);
      console.log('autocomplete-footer', footer);


      // suggestionHeader 插槽
      const headerSlots = [];
      itemGroup.children.forEach( child => {
          const header = child.children.find( item => item.name === 'suggestionHeader' );
          const headerNode = header.children[0];
          if(headerNode){
            headerSlots.push(headerNode);
          }
      })
      if(headerSlots.length){
        result.componentGuideline.push('使用suggestionHeader插槽');
        result.componentSlot.push({
          name: 'suggestionHeader',
          content: headerSlots,
        })
      }

      // 每行数据
      const suggestionColumnSlots = [];
      itemGroup.children.forEach( child => {
          const suggestionColumn = child.children.find( item => item.name === 'suggestionItemGroup' );
          if(suggestionColumn){
            suggestionColumnSlots.push(suggestionColumn);
          }
      })
      if(suggestionColumnSlots.length){
        result.componentGuideline.push('使用suggestion插槽');
      }
        result.componentSlot.push({
          name: 'suggestions',
          content: suggestionColumnSlots,
        })
      }

    // suggestionsFixedFooter 插槽
    if(footer) {
      result.componentSlot.push({
        name: 'suggestionFixedFooter',
        content: footer.children,
      })
    }
    return result;
  }
};
