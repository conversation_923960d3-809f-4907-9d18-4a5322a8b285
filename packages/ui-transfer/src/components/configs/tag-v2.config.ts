import { extractTextContent } from '../process-utils';
import { ComponentConfig } from '../types';

export const tagConfig: ComponentConfig = {
  type: 'AbcTagV2',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    console.log('progressConfig customTransformer===', { nodeContext, globalContext });
    const result: Record<string, any> = {
      componentProperties: {},
      componentGuideline: [],
      componentSlot: [],
    }
    const {
      node,
      context,
    } = nodeContext;
    // 处理图标
    const iconNode = node.children.find(child => child.name.startsWith('s-'));
    if (iconNode) {
      result.componentProperties.icon = iconNode.name;
    }
    // 处理文本
    const textNode = node.children.find(child => child.type === 'TEXT');
    if (textNode) {
      const text = extractTextContent(textNode);
      result.componentSlot.push({
        name: 'default',
        content: text,
      })
    }
    if (context.componentProperties?.icon) {
      context.componentProperties.icon = context.componentProperties['ic-name'];
    }else{
      delete context.componentProperties?.icon;
    }
    if (context.componentProperties?.['closable-inline'] === 'true') {
      context.componentProperties.closable = true
      context.componentProperties['close-position'] = 'inline'
    }
    if (context.componentProperties?.['closable-top-right'] === 'true') {
      context.componentProperties.closable = true
      context.componentProperties['close-position'] = 'top-right'
    }
    if (context.componentProperties?.minWidth === 'true') {
      context.componentProperties.closable = true
      context.componentProperties.minWidth = node.width
    }

    delete context.componentProperties?.['ic-name'];
    delete context.componentProperties?.['closable-inline'];
    delete context.componentProperties?.['closable-top-right'];
    return result;
  }
};
