import { convertNode } from '../process-utils';
import { ComponentConfig, ComponentSlot, GlobalContext, NodeContext } from '../types';

export const popoverConfig: ComponentConfig = {
  type: 'AbcPopover',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const { context, node } = nodeContext;
    
    if (!context || !context.componentProperties) {
      return {};
    }
    
    const { componentProperties } = context;
    const result = {
      componentSlot: <ComponentSlot[]>[],
      componentGuideline: [],
    };
    
    // visibleArrow 属性处理 - 是否可见
    if (componentProperties?.['visibleArrow'] === 'True') {
      componentProperties.visibleArrow = true;
    } else if (componentProperties?.['visibleArrow'] === 'False') {
      componentProperties.visibleArrow = false;
    }
    
    // size 属性处理 - 大小
    if (componentProperties?.['size']) {
      const validSizes = ['tiny', 'small', 'medium', 'large', 'huge'];
      if (!validSizes.includes(componentProperties['size'])) {
        delete componentProperties['size'];
      }
    }
    
    // placement 属性处理 - 方向位置
    if (componentProperties?.['placement']) {
      const validPlacements = ['top', 'top-start', 'top-end', 'bottom', 'bottom-start', 'bottom-end', 'left', 'left-start', 'left-end', 'right', 'right-start', 'right-end'];
      if (!validPlacements.includes(componentProperties['placement'])) {
        delete componentProperties['placement'];
      }
    }
    
    // 遍历子节点，查找是否存在指定名称的插槽
    if ('children' in node && node.children?.length) {
      console.log('AbcPopover', node);
      
      // 定义支持的插槽名称
      const supportedSlots = [
        'popover',    // 弹出层
        'reference'   // 挂载的节点
      ];
      
      // 遍历子节点
      for (const child of node.children) {
        if (child.name) {
          const nodeName = child.name.toLowerCase();
          // 检查节点名称是否匹配支持的插槽
          if(supportedSlots.includes(nodeName)) {
            if (nodeName === 'reference') {
              result.componentGuideline.push(`使用 AbcPopover 的 ${nodeName} slot, 格式为 <div slot="${nodeName}">`);  
            }
            let targetNode = null;
            
            if(nodeName === 'popover' && child.children?.length) {
              targetNode = child.children.find( iChild => iChild.name === 'content');
            }
            
            if (nodeName === 'reference' && child.children?.length) {
              targetNode = child;
            }
            // 如果有子节点，则转换子节点
            const slotNodeContext = {
              node: targetNode
            };
            if(targetNode) {
              const slotNode = await convertNode(slotNodeContext, globalContext);
              result.componentSlot.push({
                name: nodeName === 'popover' ? 'default' : nodeName,
                content: slotNode,
              });  
            }
          }
        }
      }
    }
    
    // 如果没有指南，删除指南属性
    if (result.componentGuideline.length === 0) {
      delete result.componentGuideline;
    }
    
    // 如果没有插槽，删除插槽属性
    if (result.componentSlot.length === 0) {
      delete result.componentSlot;
    }
    delete componentProperties['reference'];
    delete componentProperties['instance'];
    return result;
  }
};
