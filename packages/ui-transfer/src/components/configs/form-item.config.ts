import { ComponentConfig, GlobalContext, NodeContext } from '../types';
import {convertNode} from "@/components/process-utils";

export const formItemConfig: ComponentConfig = {
  type: 'AbcFormItem',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentSlot: [],
    };

    // 基本参数检查
    if (!nodeContext || !nodeContext.node) {
      return result;
    }

    console.log('formItemConfig customTransformer', nodeContext)

    const { context, node } = nodeContext;
    const componentProperties = context?.componentProperties;

    if (componentProperties) {
      try {
        // 查找 label 节点 - 添加类型检查
        let labelNode: any = null;
        if (node && 'children' in node && Array.isArray((node as any).children)) {
          labelNode = (node as any).children?.find((child: any) =>
            child && child.name && typeof child.name === 'string' &&
            child.name.toLowerCase() === 'label'
          );
        }

        if (labelNode && labelNode.componentProperties) {
          const properties: Record<string, any> = {};

          // 处理 labelNode 的 componentProperties
          try {
            for (const [key, value] of Object.entries(labelNode.componentProperties || {})) {
              if (typeof key === 'string' && key.includes('#')) {
                const [propName] = key.split('#');
                if (propName) {
                  properties[propName] = value;
                }
              } else {
                properties[key] = value;
              }
            }


            // 设置 label 属性
            if (properties['label'] && properties['label'].value) {
              componentProperties.label = properties['label'].value;
            }
          } catch (error) {
            console.warn('Error processing label properties:', error);
          }
        }

        const gridColumnCount = node.parent?.componentProperties?.gridColumnCount.value|| 0;
        const parentNodeWidth = node.parent?.width || 0;

        if (gridColumnCount) {

          const columnWidth = parentNodeWidth / gridColumnCount;
          const spanCount = Math.ceil(node.width / columnWidth);
          const gridColumn = spanCount;
          if (!isNaN(gridColumn) && gridColumn > 0) {
            componentProperties.gridColumn = `span ${gridColumn}`;
          } else {
            componentProperties.gridColumn = 'span 1';
          }
        }

        // 处理 isExcel 属性
        if (componentProperties.isExcel !== undefined) {
            componentProperties.isExcel = componentProperties.isExcel.toLowerCase() === 'true';
        }

      } catch (error) {
        console.warn('Error in formItemConfig customTransformer:', error);
      }
    }

    const inputNode = node.children.length > 1 ? node.children[1] : node.children[0];
    if (inputNode) {
      const inputNodeContext = {
        node: inputNode,
      };
      const inputNodeResult = await convertNode(inputNodeContext, globalContext);
      if (inputNodeResult) {
        result.componentSlot.push({
          name: 'default',
          content: inputNodeResult,
        });
      }
    }


    return result;
  }
};
