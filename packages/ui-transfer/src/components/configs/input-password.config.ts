import { ComponentConfig, GlobalContext, NodeContext } from '../types';

export const inputPasswordConfig: ComponentConfig = {
  type: 'AbcInputPassword',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const { context, node } = nodeContext;
    
    if (!context || !context.componentProperties) {
      return {};
    }
    
    const { componentProperties } = context;


    // free input 属性处理 - 是否允许自由输入
    if (componentProperties?.['freeInput'] === 'True') {
      componentProperties.freeInput = true;
    } else {
      componentProperties.freeInput = false;
    }

    // number 属性处理 - 可能是最大长度或其他数值限制
    if (componentProperties?.['number'] && !isNaN(Number(componentProperties['number']))) {
      componentProperties.maxLength = Number(componentProperties['number']);
    }


    // 清理不需要的属性
    if (componentProperties?.['free input']) {
      delete componentProperties['free input'];
    }
    if (componentProperties?.['state']) {
      delete componentProperties['state'];
    }
    if (componentProperties?.['number']) {
      delete componentProperties['number'];
    }
    if (componentProperties?.['allow exceed length']) {
      delete componentProperties['allow exceed length'];
    }

    return {};
  }
};
