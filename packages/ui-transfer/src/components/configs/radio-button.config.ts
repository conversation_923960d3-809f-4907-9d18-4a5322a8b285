import { ComponentConfig, GlobalContext, NodeContext } from '../types';

export const radioButtonConfig: ComponentConfig = {
  type: 'AbcRadioButton',
  isContainer: true,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const { context, node } = nodeContext;
    
    if (!context || !context.componentProperties) {
      return {};
    }
    
    const { componentProperties } = context;
    
    // 宽度设置
    if (componentProperties?.['width'] || node.width) {
      componentProperties.width = Number(componentProperties.width || node.width);
    }

    // variant 处理 - card 或者 default
    if (componentProperties?.['variant']) {
      if (componentProperties['variant'] === 'card' || componentProperties['variant'] === 'default') {
        // 保留有效的 variant 值
      } else {
        delete componentProperties['variant'];
      }
    }

    // checkIcon 处理 - 是否显示选中icon
    if (componentProperties?.['checkIcon'] === 'True') {
      componentProperties.checkIcon = true;
    } else {
      componentProperties.checkIcon = false;
    }

    // theme 处理 - gray 或者 default
    if (componentProperties?.['theme']) {
      if (componentProperties['theme'] === 'gray' || componentProperties['theme'] === 'default') {
        // 保留有效的 theme 值
      } else {
        delete componentProperties['theme'];
      }
    }

    // size 处理
    if (componentProperties?.['size']) {
      const validSizes = ['tiny', 'small', 'medium', 'large', 'huge'];
      if (!validSizes.includes(componentProperties['size'])) {
        delete componentProperties['size'];
      }
    }

    return {};
  }
};
