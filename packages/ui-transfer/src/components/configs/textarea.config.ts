import { ComponentConfig, GlobalContext, NodeContext } from '../types';

export const textareaConfig: ComponentConfig = {
  type: 'AbcTextarea',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const { context, node } = nodeContext;
    
    if (!context || !context.componentProperties) {
      return {};
    }
    
    const { componentProperties } = context;
    
    // 宽度设置
    if (componentProperties?.['width'] || node.width) {
      componentProperties.width = Number(componentProperties.width || node.width);
    }

    // size 处理
    if (componentProperties?.['size']) {
      const validSizes = ['tiny', 'small', 'medium', 'large', 'huge'];
      if (!validSizes.includes(componentProperties['size'])) {
        delete componentProperties['size'];
      }
    }

    // placeholder 处理
    if (!componentProperties?.['placeholder']) {
      delete componentProperties?.['placeholder'];
    }

    // 处理布尔值属性
    // disabled
    if (componentProperties?.['disable'] === 'True') {
      componentProperties.disabled = true;
    } else {
      componentProperties.disabled = false;
    }

    // readonly
    if (componentProperties?.['readonly'] === 'True') {
      componentProperties.readonly = true;
    } else if (componentProperties?.['readonly'] === 'False') {
      componentProperties.readonly = false;
    }

    // focus
    if (componentProperties?.['focus'] === 'True') {
      componentProperties.autofocus = true;
    } else {
      componentProperties.autofocus = false;
    }

    // states 处理
    if (componentProperties?.['states'] === 'disable') {
      componentProperties.disabled = true;
    }
    // 清理不需要的属性
    if (componentProperties?.['states']) {
      delete componentProperties['states'];
    }
    if (componentProperties?.['focus']) {
      delete componentProperties['focus'];
    }
    if (componentProperties?.['remind']) {
      delete componentProperties['remind'];
    }

    return {};
  }
};
