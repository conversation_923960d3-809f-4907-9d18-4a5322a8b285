import { ComponentConfig, NodeContext, GlobalContext } from '../types';

export const BizExamBusinessTag: ComponentConfig = {
  type: 'BizExamBusinessTag',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
      componentSlot: [],
      componentGuideline: [],
    };
    
    const { node, context } = nodeContext;
    const { componentProperties } = context;

    const containProps = [];

    if(componentProperties.isCloudTag === 'true') {
      componentProperties.cloudSupplierFlag = 1;
      containProps.push('isCloudTag', 'cloudSupplierFlag');
    } else if(componentProperties.isOutSourcingTag === 'true') {
      componentProperties.coopFlag = 2;
      containProps.push('isOutSourcingTag', 'coopFlag','type','subType','isText');
    }
    
    componentProperties.type = 'GoodsTypeEnum.EXAMINATION';
    if(componentProperties.isExamination === 'true') {
      componentProperties.subType = 'GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test';
    } 
    if(componentProperties.isInspect === 'true') {
      componentProperties.subType = 'GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect';
    }
    let _componentProperties = {}
    containProps.forEach(key => { 
      _componentProperties[key] = componentProperties[key];
    })
    context.componentProperties = _componentProperties;
    return result;
  }
};
