import { ComponentConfig, ComponentSlot, NodeContext } from '../types';

export const paginationConfig: ComponentConfig = {
  type: 'AbcPagination',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext) => {
    const result: Record<string, any> = {
      componentSlot: <ComponentSlot[]>[],
      componentGuideline: [],
    };
    const {context, node} = nodeContext;

    const componentProperties = context?.componentProperties;

    console.log('paginationConfig customTransformer', {context, nodeContext});

    if (componentProperties) {
      // 处理 size 属性
      if (componentProperties.size) {
        const validSizes = ['small', 'normal'];
        if (!validSizes.includes(componentProperties.size)) {
          componentProperties.size = 'normal';
        }
      }

      // 处理 showTotalPage 属性
      if (componentProperties.showTotalSum === 'True') {
        componentProperties.showTotalPage = true;
      } else {
        componentProperties.showTotalPage = false;
      }

      // 处理 showSize 属性
      if (componentProperties.showPageSize === 'False') {
        componentProperties.showSize = false;
      } else {
        componentProperties.showSize = true;
      }


      // 处理 isBackground 属性
      if (componentProperties.isBackground.toLowerCase() === 'false') {
        componentProperties.isBackground = false;
      } else {
        componentProperties.isBackground = true;
      }


      // 处理 showDetails 属性及相关插槽
      if (componentProperties.showDetails.toLowerCase() === 'true') {
        // 找到 name 为tipsContent 的节点
        const tipsContentNode = node.children?.find(child => child.name?.toLowerCase() === 'tipscontent');
        result.componentGuideline.push('使用 AbcPagination 的 tipsContent 插槽，根据设计图实现对数据的描述');
        // 遍历 children 查找名为 tipsContent 的节点
        let htmlContent = '<ul slot="tipsContent">';

        for (const child of tipsContentNode.children) {
          console.log('child', child, node)
          if (child.children && child.children.length > 0) {
            // 生成HTML结构
            // 遍历第一层children
            for (const item of child.children) {
              htmlContent += '<li>';

              // 获取文本和字体粗细
              if (item.type === 'TEXT') {
                const textNode = item;
                const text = textNode.characters || '';
                const fontWeight = textNode.fontWeight || 0;
                // 根据fontWeight决定是否加粗
                if (fontWeight > 400) {
                  htmlContent += `<span style="font-weight: bold">${text}</span>`;
                } else {
                  htmlContent += `${text}`;
                }
              }
              htmlContent += '</li>';
            }

          }

          htmlContent += '</ul>';
          console.log('htmlContent', tipsContentNode, htmlContent);
          result.componentSlot.push({
            name: 'tipsContent',
            content: htmlContent,
          });
        }
        console.log('showDetails', result.componentSlot);

        delete componentProperties.showPageSize;
        delete componentProperties.showDetails;
        delete componentProperties.showTotalSum;
      }

      // 如果没有指南，删除该属性
      if (!(result.componentGuideline as string[]).length) {
        delete result.componentGuideline;
      }

      // 如果没有插槽，删除该属性
      if (!(result.componentSlot as ComponentSlot[]).length) {
        delete result.componentSlot;
      }


      return result;
    }
  }
};
