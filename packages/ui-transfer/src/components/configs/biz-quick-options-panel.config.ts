import {
    ComponentConfig,
    ComponentSlot, GlobalContext,
    NodeContext,
} from "@/components/types";
import {convertNode} from "@/components/process-utils";

export const bizQuickOptionsPanelConfig: ComponentConfig = {
    type: "BizQuickOptionsPanel",
    isContainer: false,
    customTransformer: async (
        nodeContext: NodeContext,
        globalContext: GlobalContext,
    ) => {
        const result: Record<string, any> = {
            componentSlot: <ComponentSlot[]>[],
            componentGuideline: [],
        };

        const { context, node } = nodeContext;

        /**
         * 使用可选的组件属性
         */
        const componentProperties = context?.componentProperties;

        if (componentProperties) {
            if (componentProperties.showSetting === 'false' || componentProperties.showSetting === false) {
                delete componentProperties.showSetting;
            } else if (componentProperties.showSetting === 'true') {
                componentProperties.showSetting = true;
            }
            if (componentProperties.showSplitLine === 'true' || componentProperties.showSplitLine === true) {
                delete componentProperties.showSplitLine;
            } else if (componentProperties.showSplitLine === 'false') {
                componentProperties.showSplitLine = false;
            }
            if (componentProperties.vertical === 'false' || componentProperties.vertical === false) {
                delete componentProperties.vertical;
            } else if (componentProperties.vertical === 'true') {
                componentProperties.vertical = true;
            }
            if (componentProperties.tabsOptions === true || componentProperties.tabsOptions === 'true') {
                delete componentProperties.tabsOptions;
                componentProperties.tabsValue = '';
                componentProperties.tabsOptions = [];
            } else if (componentProperties.tabsOptions === false || componentProperties.tabsOptions === 'false') {
                componentProperties.tabsOptions = false;
            }
            if (componentProperties.header === 'true' || componentProperties.header === true) {
                delete componentProperties.header;
                const headerChildNode = (node.children || []).find((child: any) => child.name === 'header' || child.name.toLowerCase() === 'header');
                if (headerChildNode) {
                    result.componentGuideline.push('使用 BizQuickOptionsPanel 的 header 插槽');
                    if (headerChildNode.children && headerChildNode.children.length > 0) {
                        const headerNodeContext = {
                            node: headerChildNode.children[0]
                        };
                        const headerNode = await convertNode(headerNodeContext, globalContext);
                        result.componentSlot.push({
                            name: 'header',
                            content: headerNode,
                        });
                    }
                }
            }
        }

        // 如果没有指南，删除该属性
        if (!(result.componentGuideline as string[]).length) {
            delete result.componentGuideline;
        }

        // 如果没有插槽，删除该属性
        if (!(result.componentSlot as ComponentSlot[]).length) {
            delete result.componentSlot;
        }

        return result;
    },
};
