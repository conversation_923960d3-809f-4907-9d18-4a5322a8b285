import {ComponentConfig, GlobalContext, NodeContext} from '../types';

export const textConfig: ComponentConfig = {
    type: 'AbcText',
    isContainer: false,
    customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
        const result: Record<string, any> = {
            componentSlot: <ComponentSlot[]>[],
            componentGuideline: [],
        };

        const {node, context} = nodeContext;

        const {
            componentProperties
        } = context;

        if (componentProperties) {

            componentProperties.size = componentProperties.size.split(' ')?.[0] || 'normal';

            if (componentProperties.bold === 'false') {
                delete componentProperties.bold;
            }

        }


        // 处理文本内容，添加到默认插槽
        result.componentSlot.push({
            name: 'default',
            content: node?.children?.[0].characters || componentProperties['entext']
        })

        // 如果有text属性，则删除，因为已经使用默认插槽
        if (componentProperties) {
            delete componentProperties.text;
            delete componentProperties['type'];
            delete componentProperties['CN'];
            delete componentProperties['EN'];
            delete componentProperties['cntext'];
            delete componentProperties['entext'];
        }

        // 如果没有插槽内容，则删除插槽属性
        if (result.componentSlot.length === 0) {
            delete result.componentSlot;
        }
        return result;
    }
};
