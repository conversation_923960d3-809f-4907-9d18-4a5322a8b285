import { ComponentConfig, GlobalContext, NodeContext } from '../types';
import {convertNode, extractTextContent} from "@/components/process-utils";
import {isTruthyForFigma} from "@/utils/utils";

export const descriptionsConfig: ComponentConfig = {
  type: 'AbcDescriptions',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
      componentGuideline: [],
      componentSlot: [],
    };

    const { node, context } = nodeContext;

    result.componentGuideline.push('根据截图分析 descriptions 需要的信息，尤其注意标题，每个 item 的 span 等，使用 abc-descriptions 搭配 abc-descriptions-item 使用');

    // 先看下是否解组了
    const frameNode = node as InstanceNode;
    const lastRow = frameNode.children[frameNode.children.length - 1];
    const lastRowFirstItem = lastRow?.children?.[0];
    const itemLength =  frameNode.children?.find(child => child.name === 'Descriptions')?.length;
    const componentProperties = context.componentProperties;
    if (!componentProperties) {
      // 读取不到，说明被解组了，需要特殊处理下
      // 处理 size，取最后一个子节点的，height 推测 size
      
      const height = lastRow.height;
      const sizeMap = {
        28: 'small',
        32: '',
        40: 'large',
      };
      const size = sizeMap[height];
      if (size) {
        result.componentProperties.size = size;
      }

      // 判断是否是 grid，取最后一行第一个子节点 title 的 theme
      console.log('lastRowFirstItem', lastRowFirstItem);
      if (lastRowFirstItem?.children?.[0]?.componentProperties?.theme?.value === 'grid') {
        result.componentProperties.grid = true;
        result.componentProperties.bordered = true;
        result.componentGuideline.push('通过给 AbcDescriptions 设置 grid 为 true 实现网格布局');
      }
    } else {
      componentProperties.column=itemLength;
      if (componentProperties.theme === 'grid') {
        delete componentProperties.theme;
        componentProperties.grid = true;
        result.componentProperties.bordered = true;
        result.componentGuideline.push('通过给 AbcDescriptions 设置 grid 为 true 实现网格布局');
      }
      if (componentProperties.theme === 'default') {
        delete componentProperties.theme;
      }
      if (componentProperties.background === 'default') {
        delete componentProperties.background;
      } else if (componentProperties.background === 'yellow') {
        componentProperties.background = true;
      }
      if (componentProperties.form === 'false') {
        delete componentProperties.form;
      }
      if (componentProperties.customTitleStyle === 'false' || !componentProperties.customTitleStyle) {
        delete componentProperties.customTitleStyle;
      }
      if (componentProperties.disabled === 'false') {
        delete componentProperties.disabled;
      }
      if (componentProperties.size === 'default') {
        delete componentProperties.size;
      }
      if (isTruthyForFigma(componentProperties.title)) {
        const titleContentNode = frameNode.children?.find(child => child.name === 'title')?.children?.find(child => child.name === 'AbcText');
        if (titleContentNode) {
          result.componentProperties.title = extractTextContent(titleContentNode.children?.[0]);
        }
        // 处理 slot
        const headerNode = frameNode.children.find(child => child.name.toLowerCase() === 'title')?.children?.find(child => child.name === 'slot');
        const headerNodeContext: NodeContext = {
          node: headerNode as SceneNode,
        };
        const headerSlotInfo = await convertNode(headerNodeContext, globalContext);
        if (headerSlotInfo) {
          result.componentSlot.push({
            slotName: 'title',
            figmaInfo: headerSlotInfo
          });
        }else{
          result.componentGuideline.push('从截图中分析弹窗标题，并给 AbcDialog 的 title 属性设置')
        }
        delete componentProperties.title;
      }else{
        delete componentProperties.title;
      }
      if (componentProperties.bordered === 'false') {
        componentProperties.bordered = false;
      } else {
        componentProperties.borderStyle = componentProperties.bordered;
      }

      // 处理 padding
      if (frameNode.paddingTop === 0 || frameNode.paddingRight === 0 || frameNode.paddingBottom === 0 || frameNode.paddingLeft === 0) {
        componentProperties.contentPadding = '0';
      }
    }
    // 根据 lastRowFirstItem 推断 labelWidth
    if (lastRowFirstItem) {
      const labelWidth = lastRowFirstItem.children?.[0]?.width;
      if (labelWidth) {
        result.componentProperties = {
          ...result.componentProperties,
          labelWidth
        }
        result.componentGuideline.push(`通过给 AbcDescriptions 的 labelWidth 设置为 ${labelWidth} 实现自定义 label 宽度`);
      }
    }
    return result;
  }
};
