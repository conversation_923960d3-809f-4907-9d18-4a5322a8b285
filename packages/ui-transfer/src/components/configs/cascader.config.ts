import {ComponentConfig} from '../types';
import {convertNode} from "@/components/process-utils";

export const cascaderConfig: ComponentConfig = {
    type: 'AbcCascader',
    isContainer: false,
    async customTransformer(nodeContext, globalContext) {
        const result: Record<string, any> = {};

        const {
            context,
            node
        } = nodeContext;

        const {componentProperties} = context!;

        if (componentProperties) {
            const inputNode = node.children?.find(e => e.name === 'AbcInput');
            const panelNode = node.children?.find(e => e.name === 'panel-group' || e.name === 'itemgroup');

            if (inputNode) {
                componentProperties.width = inputNode.width;

                const inputComponent = await convertNode({node: inputNode,}, globalContext);

                console.log('inputComponent', inputComponent)

                if (inputComponent?.componentProperties) {
                    componentProperties.placeholder = inputComponent.componentProperties.placeholder;
                    componentProperties.clearable = inputComponent.componentProperties.clearable;
                    componentProperties.onlyBottomBorder = inputComponent.componentProperties.onlyBottomBorder;
                    componentProperties.noIcon = !!inputComponent.componentProperties.componentSlot?.find(item => item.name === 'appendInner');
                    componentProperties.size = inputComponent.componentProperties.size;
                    componentProperties.adaptiveWidth = inputComponent.componentProperties.adaptiveWidth;
                }
            }

            if (panelNode) {
                componentProperties.panelMaxHeight = panelNode.height;
            }

            delete componentProperties.states;
            delete componentProperties.expand;

            // 删除对象上面值为undefined的属性
            Object.keys(componentProperties).forEach(key => {
                if (componentProperties[key] === undefined) {
                    delete componentProperties[key];
                }
            });

        }

        return result;
    }
};
