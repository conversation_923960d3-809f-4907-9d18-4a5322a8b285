import { ComponentConfig, GlobalContext, NodeContext } from '../types';

export const radioConfig: ComponentConfig = {
  type: 'AbcRadio',
  isContainer: false,
  nodeWhitelist: ["radio base"],
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const { context, node } = nodeContext;
    
    if (!context || !context.componentProperties) {
      return {};
    }
    
    const { componentProperties } = context;
    const result = {};

    // size 处理
    if (componentProperties?.['size']) {
      const validSizes = ['tiny', 'small', 'medium', 'large', 'huge'];
      if (!validSizes.includes(componentProperties['size'])) {
        delete componentProperties['size'];
      }
    }

    // enableCancel 处理 - 是否允许反选
    if (componentProperties?.['enableCancel'] === 'True') {
      componentProperties.enableCancel = true;
    } else {
      componentProperties.enableCancel = false;
    }

    // disabled 处理
    if (componentProperties?.['disabled'] === 'True') {
      componentProperties.disabled = true;
    } else {
      componentProperties.disabled = false;
    }

    // 处理子内容作为默认插槽
    if ('children' in node && node.children?.length) {
      const textNodes = node.children?.filter(child => child.type === 'TEXT');
      if (textNodes.length > 0) {
        const defaultSlot = {
          name: 'default',
          content: textNodes[0],
        };
        return { componentSlot: [defaultSlot] };
      }
    }
    delete  componentProperties['states'];
    delete componentProperties['disable'];
    delete componentProperties['theme'];
    return result;
  }
};
