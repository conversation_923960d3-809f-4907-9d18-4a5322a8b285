import { ComponentConfig, GlobalContext, NodeContext } from '../types';

export const contentEmptyConfig: ComponentConfig = {
  type: 'AbcContentEmpty',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {};
    const { context } = nodeContext;
    
    const componentProperties = context?.componentProperties;

    if (componentProperties) {
      console.log('AbcContentEmpty componentProperties ==> ', componentProperties);

      // 处理 size 属性
      if (componentProperties.size) {
        const validSizes = ['mini', 'small', 'normal', 'large'];
        if (!validSizes.includes(componentProperties.size)) {
          delete componentProperties.size;
        }
      }

      // 处理 iconName 属性
      if (!componentProperties.iconName) {
        delete componentProperties.iconName;
      }

      // 处理 iconSize 属性
      if (componentProperties.iconSize) {
        componentProperties.iconSize = Number(componentProperties.iconSize) || undefined;
        if (!componentProperties.iconSize) {
          delete componentProperties.iconSize;
        }
      }

      // 处理 iconColor 属性
      if (!componentProperties.iconColor) {
        delete componentProperties.iconColor;
      }

      // 处理 showIcon 属性
      if (componentProperties['show-icon'] === 'False') {
        componentProperties.showIcon = false;
      } else {
        componentProperties.showIcon = true;
      }
      // 处理 content 属性
      if (componentProperties.content) {
        componentProperties.value = componentProperties.content;
      } 
      delete componentProperties.content;
    }

    return result;
  }
};
