import { ComponentConfig, GlobalContext, NodeContext } from '../types';

export const optionCardConfig: ComponentConfig = {
  type: 'AbcOptionCard',
  isContainer: true,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentProperties: {}
    };

    const { node, context } = nodeContext;

    if (!context?.componentProperties) {
      return result;
    }

    const { componentProperties } = context;

    // 处理 icon 属性 - 控制是否显示图标
    if (componentProperties.icon !== undefined) {
      const iconProp = componentProperties.icon?.toLowerCase();
      if (iconProp === 'false') {
        result.componentProperties.showIcon = false;
      } else {
        result.componentProperties.showIcon = true;
      }
    }
    delete componentProperties.icon;


    // 支持的 selectable 属性  'icon-inside', 'icon-outside', 'default', 'false'
    const validSelectable = ['icon-inside', 'icon-outside', 'default', 'false'];
    if (!validSelectable.includes(componentProperties.selectable) || componentProperties.selectable === undefined) {
      delete componentProperties.selectable;
    }

    // 处理 states 属性 - 状态处理
    if (componentProperties.states !== undefined) {
      if (componentProperties.states === 'checked') {
        result.componentProperties.value = true;
      }
      // 删除 states 属性，因为组件不直接支持
      delete componentProperties.states;
    }



    // 处理 theme 属性
    if (componentProperties.theme !== undefined) {
      const validThemes = ['primary', 'success'];
      if (validThemes.includes(componentProperties.theme)) {
        result.componentProperties.theme = componentProperties.theme;
      }
    }

    // 处理宽度
    const width = node?.width || 90;
    if (width) {
      result.componentProperties.width = width;
    }

    return result;
  }
};
