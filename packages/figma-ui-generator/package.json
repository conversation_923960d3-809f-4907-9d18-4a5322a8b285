{"name": "@abc-figma/figma-ui-generator", "version": "1.0.0", "description": "Convert Figma designs to ABC UI components", "main": "code.js", "scripts": {"build": "webpack --mode=production", "dev:plugin": "webpack --mode=development --watch", "dev:ui": "webpack serve --mode=development", "dev1": "pnpm run dev:plugin & pnpm run dev:ui", "dev": "webpack --mode=development --watch", "doc-gen": "node tools/csf2sfc/index.js", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint --ext .ts,.tsx --ignore-pattern node_modules .", "lint:fix": "eslint --ext .ts,.tsx --ignore-pattern node_modules --fix .", "watch": "npm run build -- --watch"}, "keywords": ["figma", "plugin", "abc-ui"], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.26.0", "@babel/generator": "^7.26.5", "@babel/parser": "^7.26.5", "@babel/traverse": "^7.26.5", "@babel/types": "^7.26.5", "@figma/eslint-plugin-figma-plugins": "*", "@figma/plugin-typings": "^1.106.0", "@types/node": "^20.10.6", "@types/webpack-env": "^1.18.5", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@vue/compiler-sfc": "3.2.30", "clean-webpack-plugin": "^4.0.0", "css-loader": "^6.8.1", "eslint": "^8.54.0", "file-loader": "^6.2.0", "html-inline-script-webpack-plugin": "^3.2.1", "html-webpack-plugin": "^5.6.0", "prettier": "^3.5.3", "remark-mdx": "^3.1.0", "remark-parse": "^11.0.0", "sass": "1.69.7", "sass-loader": "^13.3.2", "style-loader": "^3.3.3", "ts-loader": "^9.5.1", "typescript": "^5.3.3", "unified": "^11.0.5", "unist-util-visit-parents": "^6.0.1", "vue": "2.7.14", "vue-loader": "^15.11.1", "vue-style-loader": "^4.1.3", "vue-template-compiler": "2.7.14", "vue3-sfc-loader": "^0.9.5", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.2.2"}, "eslintConfig": {"extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@figma/figma-plugins/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json"}, "root": true, "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}]}}, "dependencies": {"@abc-figma/ui-transfer": "workspace:*", "@abc/pc-components": "^1.0.7", "@abc/ui-pc": "^1.432.6", "ace-builds": "^1.43.2", "deepmerge": "^4.3.1"}}