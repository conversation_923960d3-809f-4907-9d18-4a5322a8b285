<template>
  <div id="app">
    <div class="layout">
      <div class="header">
        <div class="button-group">
          <button :disabled="swimming" @click="handleQuickPreview">
            {{ swimming ? '代码生成中' : '快速预览' }}
          </button>
          <button :disabled="swimming" @click="handleConvertSmart">生成提示词</button>
          <button @click="handleInspect">Debug</button>
        </div>
      </div>

      <div class="card">
        <div class="flex-vertical">
          <textarea
              v-model="currentPrompt"
              maxlength="100000"
              placeholder="请输入提示词"
              class="prompt-textarea"
              rows="15"
          ></textarea>
          <div class="flex-horizontal">
            <select v-model="llmConfig.model" @change="handleModelChange">
              <option
                  v-for="item in models"
                  :key="item.value"
                  :value="item.value"
              >{{ item.label }}</option>
            </select>
            <label class="checkbox-label">
              <input type="checkbox" v-model="deepSeek" @change="handleDeepSeek">
              深度思考
            </label>

            <div class="button-group-right">
              <button
                  :disabled="!currentPrompt"
                  title="复制提示词"
                  @click="handleCopyPrompt"
              >复制</button>

              <button
                  :disabled="!currentPrompt"
                  :title="swimming ? '结束生成' : '生成代码'"
                  @click="handleSubmit"
              >{{ swimming ? '结束' : '生成' }}</button>
            </div>
          </div>
        </div>
      </div>
      <div v-if="deepSeek" class="card thinking-card">
        <textarea
            v-model="deepseekResult.thinking"
            readonly
            placeholder="思考中"
            class="thinking-textarea"
            rows="10"
        ></textarea>
      </div>

      <div class="card main-card">
        <div class="tabs">
          <button
            :class="{ active: currentTab === 'code' }"
            @click="currentTab = 'code'; handleTabChange()"
          >代码</button>
          <button
            :class="{ active: currentTab === 'preview' }"
            @click="currentTab = 'preview'; handleTabChange()"
          >预览</button>
        </div>
        <div v-if="currentTab === 'preview'" class="preview-container">
          <component :is="dynamicComponent" v-if="dynamicComponent"></component>
        </div>
        <div ref="codeContainer" v-show="currentTab === 'code'" class="code-container">
          <div ref="editor" class="code-editor"></div>
        </div>
      </div>

      <div v-if="currentTab === 'code'" class="button-group-bottom">
        <button :disabled="!deepseekResult.content" @click="handleCopyCode">复制代码</button>
        <button :disabled="!deepseekResult.content" @click="formatCode">格式化代码</button>
      </div>

    </div>
  </div>
</template>

<script>
import Vue from 'vue';
import {loadModule} from 'vue3-sfc-loader/dist/vue2-sfc-loader';
// import LocalPreview from "./local-preview.vue";
// import ErrorBoundary from "./error-boundary.vue";
import prettier from "prettier/standalone";
import htmlPlugin from "prettier/plugins/html";
import ace from 'ace-builds';
// import 'ace-builds/webpack-resolver'; // 添加 webpack resolver
import 'ace-builds/src-noconflict/theme-monokai';
import 'ace-builds/src-noconflict/ext-language_tools'; // 自动补全
import 'ace-builds/src-noconflict/mode-vue';


export default {
  name: 'App',
  // components: {
  //   ErrorBoundary
  // },
  data() {
    return {
      dynamicComponent: null,
      copyButtonText: "复制",
      currentPrompt: "",
      showComponent: false,

      currentTab: 'code',// code, preview
      options: [
        {label: "代码", value: "code"},
        {label: "预览", value: "preview"},
      ],
      llmConfig: {
        url: 'https://dev-oa.abczs.cn/api/monitor/chat/completions',
        // url: 'https://localhost:3070/api/monitor/chat/completions',
        model: "deepseek-v3-1-250821",
        thinking: {
          type: "disabled" // enabled, disabled, auto
        },
      },
      models: [
        {label: "deepseek-v3.1", value: "deepseek-v3-1-250821"},
        {label: "deepseek-r1", value: "deepseek-r1-250528"},
        {label: "doubao-1.6-vision", value: "doubao-seed-1-6-vision-250815"},
        {label: "doubao-1.6", value: "doubao-seed-1-6-250615"},
        {label: "doubao-1.6-flash", value: "doubao-seed-1-6-flash-250715"},
      ],
      // 是否开启深度思考
      deepSeek: false,
      // 生成中
      swimming: false,
      // 对话框缩放相关
      dialogScale: 1,
      minScale: 0.1,
      maxScale: 1.5,
      scaleStep: 0.01,
      deepseekResult: {
        thinking: '',
        content: '',
        loading: false,
        isPreparing: false, // 准备用于基本信息 - thinking 之前
        error: null,
      },
      MONACO_EDITOR_OPTIONS: {
        automaticLayout: true,
        formatOnType: true,
        formatOnPaste: true,
      },
      editor: null,
      currentController: null,
    }
  },
  created() {
    parent.postMessage({
      pluginMessage: {
        type: 'get-local-storage', data: {
          key: 'llmConfig',
          defaultValue: this.llmConfig
        }
      }
    }, '*')
    // 监听来自插件的消息
    window.onmessage = async (event) => {
      const msg = event.data.pluginMessage;
      if (msg) {
        // 更新提示词
        if (msg.type === 'update-prompt') {
          this.currentPrompt = msg.prompt;
          if (msg.isStartSwimming) {
            this.generateCode();
          }
        } else if (msg.type === 'figma-storage') {
          console.log(msg.type, msg.data);
          if (msg.data) {
            if (msg.data.key === 'llmConfig') {
              this.llmConfig = {
                ...this.llmConfig,
                ...msg.data.value
              };
              this.deepSeek = this.llmConfig?.thinking?.type === 'enabled';
            }
          }
        }
      }
    }
  },
  mounted() {
    this.initEditor();
  },
  beforeDestroy() {
    this.cancelLLMRequest();
  },
  errorCaptured(err, vm, info) {
    console.log('App errorCaptured', err, vm, info);
    this.$alert({
      type: 'warn',
      title: '错误提示',
      content: err
    })
    return false; // 阻止错误继续向上传播
  },
  methods: {
    cancelLLMRequest() {
      if (this.currentController) {
        this.currentController.abort();
        this.currentController = null;
      }
    },
    async callLLM(input = '', llmConfig = {}) {
      const {
        url,
        model,
        thinking
      } = llmConfig;

      if (!url) {
        this.$Toast({
          message: '无效请求',
          type: 'error',
        })
        return;
      }
      this.cancelLLMRequest();
      // 检查 AbortController 是否可用
      const isAbortControllerSupported = typeof AbortController !== 'undefined';
      const fetchOptions = {
        method: "POST",
        headers: {
          'Accept': 'text/event-stream',
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          "model": model,
          "messages": [
            {
              "role": "system",
              "content": input
            },
          ],
          "thinking": thinking,
          "temperature": 0.7,
          // 配合'Accept': 'text/event-stream'使用
          "stream": true
        })
      }

      if (isAbortControllerSupported) {
        this.currentController = new AbortController();
        fetchOptions.signal = this.currentController.signal;
      }
      return fetch(url, fetchOptions).finally(() => {
        // 请求完成后清理 controller
        if (isAbortControllerSupported) {
          this.currentController = null;
        }
      });
    },
    async generateCode() {
      try {
        this.swimming = true;
        this.deepseekResult.thinking = '';
        this.deepseekResult.content = '';
        const response = await this.callLLM(this.currentPrompt, this.llmConfig);
        const reader = response.body.getReader();
        const decoder = new TextDecoder('utf-8');
        let buffer = '';

        while (true) {
          const {done, value} = await reader.read();
          if (done) {
            console.log('SSE流结束');
            break;
          }

          buffer += decoder.decode(value, {stream: true});
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // 取出最后一行不完整的数据放回缓冲区

          for (const line of lines) {
            if (line.startsWith('data:')) {
              const data = line.slice(5).trim();
              // console.log('收到数据:', data);
              // 进一步处理数据，例如是JSON则解析
              try {
                const parsedData = JSON.parse(data);
                // 处理 parsedData
                const {
                  choices: [{delta}]
                } = parsedData;
                // console.log('解析数据:', delta, delta.role);
                if (delta.role === "assistant") {
                  this.deepseekResult.thinking += delta.reasoning_content || '';
                  this.deepseekResult.content += delta.content || '';
                  if (this.deepseekResult.content) {
                    this.editor.setValue(this.deepseekResult.content, 1);
                  }
                  // console.log('更新数据:', this.deepseekResult.content);
                }

              } catch (e) {
                // 非JSON数据或其他处理
                console.error('JSON parse error', e);
              }
            }
            // 可处理其他事件类型，如 event: ping, id: 123, retry: 1000 等
          }
        }
        // console.log('LLM result', response);
        // const json = await response.json();
        //
        // const content = json?.choices?.[0]?.message?.content;
        // if (content) {
        //   this.deepseekResult.content = content;
        //   this.editor.setValue(content, 1);
        // } else {
        //   this.$Toast({
        //     message: '生成失败请重试',
        //     type: 'error',
        //   })
        // }
      } catch (e) {
        console.error(e);
        if (e.name === 'AbortError') {
          this.$Toast({
            message: '已取消生成',
            type: 'info',
          });
        } else {
          this.$Toast({
            message: '生成失败请重试',
            type: 'error',
          });
        }
      } finally {
        this.swimming = false;
      }

    },
    setupDialogZoom() {
      const dialog = document.querySelector('.abc-dialog');
      if (!dialog) return;

      const handleWheel = (e) => {
        // 检查是否按下了 Ctrl 键（在 Windows/Linux）或 Command 键（在 Mac）
        if (e.ctrlKey || e.metaKey) {
          e.preventDefault();

          // 计算新的缩放比例
          const delta = e.deltaY > 0 ? -this.scaleStep : this.scaleStep;
          let newScale = this.dialogScale + delta;


          // 限制缩放范围
          newScale = Math.max(this.minScale, Math.min(this.maxScale, newScale));

          // 更新缩放比例
          if (newScale !== this.dialogScale) {
            this.dialogScale = parseFloat(newScale.toFixed(2));
            dialog.style.transform = `translate(-50%, -50%) scale(${this.dialogScale})`;
            dialog.style.transformOrigin = 'center center';
          }
        }
      };

      // 添加滚轮事件监听器
      dialog.addEventListener('wheel', handleWheel, {passive: false});

      // 在组件销毁时移除事件监听器
      this.$once('hook:beforeDestroy', () => {
        dialog.removeEventListener('wheel', handleWheel);
      });
    },

    initEditor() {
      // 获取容器高度并计算行数
      const container = this.$refs.codeContainer;
      const lineHeight = 19; // 默认行高，根据实际字体大小调整
      const containerHeight = container.clientHeight - 20; // 减去横向滚动条高度
      const calculatedLines = Math.floor(containerHeight / lineHeight);

      this.editor = ace.edit(this.$refs.editor, {
        mode: 'ace/mode/vue',
        theme: 'ace/theme/monokai',
        tabSize: 2,
        useWorker: false, // 禁用 worker 以避免 JSON 解析错误时的警告
        minLines: calculatedLines,
        maxLines: calculatedLines, // 设置相同的值使高度固定
        fontSize: '14px',
        showPrintMargin: false,
        showGutter: true,
        highlightActiveLine: true,
        wrap: false,
        autoScrollEditorIntoView: true,
        enableSnippets: true,
        enableBasicAutocompletion: true,
        enableLiveAutocompletion: true
      });

      // 添加窗口大小变化监听器
      const handleResize = () => {
        const newHeight = container.clientHeight - 20;
        const newLines = Math.floor(newHeight / lineHeight);
        this.editor.setOptions({
          minLines: newLines,
          maxLines: newLines
        });
      };

      window.addEventListener('resize', handleResize);

      // 在组件销毁时移除事件监听器
      this.$once('hook:beforeDestroy', () => {
        window.removeEventListener('resize', handleResize);
      });

      this.editor.setValue(this.deepseekResult.content, 1); // 初始化值

      this.editor.getSession().on('change', () => {
        const code = this.editor.getValue();
        this.deepseekResult.content = code;
      });
    },
    // 格式化逻辑
    async formatCode() {
      console.log('formatCode', this.deepseekResult.content);
      const formatted = await prettier.format(this.deepseekResult.content, {
        parser: 'vue',
        plugins: [htmlPlugin],
        semi: true,
        singleQuote: true,
        vueIndentScriptAndStyle: true
      });

      this.deepseekResult.content = formatted
      this.editor.setValue(this.deepseekResult.content, 1);// 1不触发change
    },
    handleDeepSeek() {
      this.deepSeek = !this.deepSeek
      this.llmConfig.thinking = {
        type: this.deepSeek ? 'enabled' : 'disabled'
      }
      parent.postMessage({
        pluginMessage: {
          type: 'set-local-storage', data: {
            key: 'llmConfig',
            value: this.llmConfig
          }
        }
      }, '*')
    },
    handleModelChange() {
      console.log('handleModelChange', this.llmConfig.model)
      parent.postMessage({
        pluginMessage: {
          type: 'set-local-storage', data: {
            key: 'llmConfig',
            value: this.llmConfig
          }
        }
      }, '*')
    },
    async createComponent(code) {
      this.dynamicComponent = null;

      if (!code) return;

      console.log('开始加载动态组件...');
      const startTime = Date.now();

      try {
        const component = await this._loadComponentWithTimeout(code);
        this._handleComponentLoadSuccess(component, startTime);
      } catch (error) {
        this._handleComponentLoadError(error, code);
      }
    },

    // 带超时的组件加载
    async _loadComponentWithTimeout(code) {
      const LOAD_TIMEOUT = 10000; // 10秒超时

      const timeoutPromise = new Promise((_, reject) => {
        const timer = setTimeout(() => {
          clearTimeout(timer);
          reject(new Error(`组件加载超时，已超过 ${LOAD_TIMEOUT / 1000} 秒`));
        }, LOAD_TIMEOUT);
      });

      const loadComponent = loadModule(`/dynamicComponent.vue`, {
        moduleCache: {vue: Vue},
        getFile: (url) => {
          console.log('加载文件:', url);
          return Promise.resolve(code);
        },
        addStyle: (css) => this._addStyleToDocument(css)
      });

      return Promise.race([loadComponent, timeoutPromise]);
    },

    // 添加样式到文档
    _addStyleToDocument(css) {
      try {
        const style = document.createElement("style");
        style.textContent = css;
        document.head.appendChild(style);
        console.log('添加了样式');
      } catch (error) {
        console.error('添加样式时出错:', error);
        throw error;
      }
    },

    // 处理组件加载成功
    _handleComponentLoadSuccess(component, startTime) {
      if (!component?.render) {
        throw new Error('加载的组件无效');
      }

      console.log(`组件加载完成，耗时: ${Date.now() - startTime}ms`);
      this.dynamicComponent = component;
      console.log('动态组件加载成功', component);
    },

    // 处理组件加载错误
    _handleComponentLoadError(error) {
      console.error('创建组件时发生错误:', error);

      const errorInfo = {
        message: error.message || '加载的组件无效',
        stack: error.stack || '无堆栈信息',
        time: new Date().toISOString()
      };

      this._showComponentError(errorInfo);
    },

    // 显示组件错误
    async _showComponentError(errorInfo) {

      Vue.component('ErrorComponent', {
        template: `
          <div
              style="color: #f56c6c; padding: 20px; background: #fef0f0; border-radius: 4px; border: 1px solid #fde2e2; margin: 10px;">
            <h3 style="margin-top: 0;">组件加载失败</h3>
            <p><strong>错误信息：</strong>${errorInfo.message}</p>
            <div style="margin-top: 10px;">
              <h4>错误堆栈：</h4>
              <pre
                  style="max-height: 200px; overflow: auto; background: #f5f5f5; padding: 10px; border-radius: 4px; white-space: pre-wrap;">${errorInfo.stack || '无堆栈信息'}</pre>
            </div>
            <div style="margin-top: 15px; font-size: 12px; color: #999;">
              错误时间: ${new Date(errorInfo.time).toLocaleString()}
            </div>
          </div>
        `,
      });

      this.dynamicComponent = 'ErrorComponent';
    },
    handleQuickPreview() {
      if (this.swimming) return;
      parent.postMessage({pluginMessage: {type: 'quick-preview'}}, '*')
    },
    // 生成代码
    handleTextareaEnter() {
      if (this.swimming) return;
      this.handleSubmit();
    },
    handleSubmit() {
      if (this.swimming) {
        console.log('结束生成');
        this.cancelLLMRequest();
        return;
      }
      if (!this.currentPrompt) {
        this.$Toast({
          message: '请输入提示词'
        })
        return;
      }
      this.generateCode();
    },
    // 生成提示词
    handleConvertSmart() {
      parent.postMessage({pluginMessage: {type: 'convert-to-abc', smart: true}}, '*')
    },
    // 调试figma节点
    handleInspect() {
      parent.postMessage({pluginMessage: {type: 'inspect-selection'}}, '*');
      console.log('mini-dev-tools', this);
    },
    copyText(text) {
      // 创建一个临时的 input 元素
      const input = document.createElement('input');
      input.value = text;
      input.style.position = 'absolute';
      input.style.left = '-9999px';
      document.body.appendChild(input);

      // 选中并复制
      input.focus();
      input.select();

      try {
        // 尝试复制
        const successful = document.execCommand('copy');
        if (successful) {
          this.$Toast({
            message: '复制成功',
            type: 'success',
          })
        } else {
          this.$Toast({
            message: '复制失败',
            type: 'error',
          })
        }
      } catch (err) {
        console.error('复制失败:', err);
        this.$Toast({
          message: '复制失败',
          type: 'error',
        })
      }

      // 移除临时元素
      document.body.removeChild(input);
    },
    // 复制提示词
    handleCopyPrompt() {
      this.copyText(this.currentPrompt)
    },
    // 复制代码
    handleCopyCode() {
      this.copyText(this.deepseekResult.content)
    },
    handleTabChange() {
      if (this.currentTab === 'code') {
        this.$nextTick(() => {
          this.initEditor();
        });
      } else {
        this.dynamicComponent = null;
        this.$nextTick(() => {
          this.createComponent(this.deepseekResult.content);
          setTimeout(() => {
            this.setupDialogZoom();
          }, 1000)
        });
      }
    }
  }
}
</script>

<style lang="scss">
html, body, #app {
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 12px;
  gap: 12px;
}

.header {
  flex-shrink: 0;
}

.button-group {
  display: flex;
  gap: 8px;
}

.button-group button {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  cursor: pointer;

  &:hover {
    border-color: #40a9ff;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.card {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 16px;
  background: white;
}

.flex-vertical {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.flex-horizontal {
  display: flex;
  align-items: center;
  gap: 8px;
}

.prompt-textarea {
  width: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 8px;
  resize: vertical;
  font-family: inherit;
}

.thinking-card {
  margin-bottom: 12px;
}

.thinking-textarea {
  width: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 8px;
  resize: vertical;
  font-family: monospace;
  background: #f5f5f5;
}

.main-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #d9d9d9;
  margin-bottom: 12px;
}

.tabs button {
  padding: 8px 16px;
  border: none;
  background: none;
  cursor: pointer;
  border-bottom: 2px solid transparent;

  &.active {
    border-bottom-color: #1890ff;
    color: #1890ff;
  }
}

.preview-container {
  flex: 1;
  overflow: auto;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.code-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.code-editor {
  flex: 1;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.button-group-right {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

.button-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

select {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}
</style>