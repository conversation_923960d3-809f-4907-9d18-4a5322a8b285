import Vue from 'vue';
// import AbcUI, {setAbcTheme} from '@abc/ui-pc';
// import AbcBizUI, {i18n} from '@abc/pc-components';
// import '@abc/pc-components/lib/pc-components.min.css';
// import '@abc/ui-pc/lib/ui-pc.min.css';
// import "@abc/ui-pc/theme/common/var.scss";
// 导入 App 组件
import App from './App.vue';

// 确保 DOM 加载完成再挂载
document.addEventListener('DOMContentLoaded', () => {
  // 使用 ABC UI 组件库
  // Vue.use(AbcUI);
  // Vue.use(AbcBizUI);

  // console.log('setAbcTheme',document.documentElement);
  // 设置主题
  // setAbcTheme('pharmacy');
  // console.log('AbcBizUIAbcBizUI', AbcBizUI);
  // console.log('i18n',i18n)
  // i18n.setLocaleMessage('zh-MO', Object.assign({}, i18n.messages['zh-MO']));

  new Vue({
    el: '#app',
    // i18n,
    render: h => h(App)
  });
});
