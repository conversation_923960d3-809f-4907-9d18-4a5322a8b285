// u4ece ui-transfer u5305u4e2du5bfcu5165u7ec4u4ef6
import {ComponentConverter, nodeToJSON, componentConfigs} from '@abc-figma/ui-transfer';
// import testNode from './test-node.json';
type PluginMessageType =
    'ui-check'
    | 'open-preview'
    | 'inspect-selection'
    | 'convert-to-abc'
    | 'create-shapes'
    | 'cancel'
    | 'copy-to-clipboard'
    | 'create-screenshot'

interface PluginMessage {
    type: PluginMessageType;
    count?: number;
    smart?: boolean;
    text?: string;
}

console.log('ComponentConverter', ComponentConverter)
// 创建组件转换器实例
const converter = new ComponentConverter();

const globalNodeWhitelist = [
    'title',
    'content',
    'Button',// 包裹按钮的div应该使用abc-space
]
const checkErrors: string[] = [];
// 获取所有组件
const componentConfigMap = Object.values(componentConfigs).reduce((res: Record<string, ComponentConfig>, config: any) => {
    res[config.type] = config;

    if (config.customChildren) {
        Object.entries(config.customChildren).forEach(([key, value]) => {
            res[key] = value;
        })
    }
    return res;
}, {});


// 递归检查节点是否合法
const checkNode = (nodes: readonly SceneNode[], whitelist: string[] = globalNodeWhitelist): void => {

    for (const node of nodes) {
        const nodeWhitelist = componentConfigMap[node.name]?.nodeWhitelist;

        // 排除白名单节点，以及叶子节点一般是文本、icon
        if (!whitelist.includes(node.name) && !componentConfigMap[node.name] && node.children?.length) {
            checkErrors.push(node.name);
        }
        if (node.children?.length) {
            checkNode(node.children, globalNodeWhitelist.concat(nodeWhitelist || []));
        }
    }
};

function callLLM(input: string) {
    console.log('callLLM', input);
    const modelConfig = {
        url: "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
        apikey: "904aa43b-fe95-477c-989d-32d317e0603f",
        model: 'doubao-seed-1-6-250615'
    };
    return fetch(modelConfig.url, {
        method: "POST",
        headers: {
            "Authorization": `Bearer ${modelConfig.apikey}`,
            "Content-Type": "application/json"
        },
        body: JSON.stringify({
            "model": modelConfig.model,
            "messages": [
                {
                    "role": "system",
                    "content": '你是一名资深前端工程师，精通 Vue 和 Figma 设计稿转换。'
                },
                {
                    "role": "user",
                    "content": input
                }
            ],
            "temperature": 0.7,
            "stream": false
        })
    });
}

function generatePrompt(figmaInfo: string) {
    return `根据 figma 解析出的节点信息及推荐组件，使用 AbcUI 生成完整的 vue 单文件组件代码，
                    \n【重要】返回结果只包含 vue 单文件组件代码的纯文本字符串，不要返回其他任何无关信息，也不要代码块语法包裹内容。
                    - 不要使用import导入任何AbcUI组件，直接使用AbcUI组件即可。
                    - 使用figma提供的节点信息供参考生成 dom 结构，注意布局和排版。
                    - suggestedComponentList提供了相关组件的 API，设置属性时，需要严格遵守suggestedComponentList中声明的 props，确保组件使用正确。
                    \n${JSON.stringify(figmaInfo)}`
}

// 处理来自 UI 的消息
figma.ui.onmessage = async (msg: PluginMessage) => {
    if (msg.type === 'create-shapes') {
        const nodes: SceneNode[] = [];
        for (let i = 0; i < msg.count!; i++) {
            const rect = figma.createRectangle();
            rect.x = i * 150;
            rect.fills = [{type: 'SOLID', color: {r: 1, g: 0.5, b: 0}}];
            figma.currentPage.appendChild(rect);
            nodes.push(rect);
        }
        figma.currentPage.selection = nodes;
        figma.viewport.scrollAndZoomIntoView(nodes);
    }

    if (msg.type === 'cancel') {
        figma.closePlugin();
    }

    if (msg.type === 'inspect-selection') {
        const selection = figma.currentPage.selection;
        if (selection.length === 0) {
            figma.notify('Please select something');
            return;
        }

        const json = selection.map(node => nodeToJSON(node));
        console.log('Selected Elements:', JSON.stringify(json, null, 2));
        figma.notify('Selection info logged to console');

        // console.log('testNode', testNode.nodes['11446:60174'].document);
        // const result = await converter.smartConvert(testNode.nodes['11446:60174'].document);
        // console.log('testNode result', JSON.stringify(result, null, 2));
    }

    if (msg.type === 'create-screenshot') {
        const selection = figma.currentPage.selection;
        if (selection.length === 0) {
            figma.notify('请先选择需要截图的节点');
            return;
        }

        // 获取选中节点的边界框
        const node = selection[0];
        const imageData = await node.exportAsync({
            format: 'PNG',
            constraint: {type: 'SCALE', value: 2}
        });

        // 将 Uint8Array 转换为 Base64
        const base64Data = figma.base64Encode(imageData);

        // 发送 Base64 数据到 UI 层
        figma.ui.postMessage({
            type: "copy-to-clipboard",
            data: `data:image/png;base64,${base64Data}`,
        });

        figma.notify('截图已生成，请查看下载文件夹');
    }

    if (msg.type === 'convert-to-abc') {
        const selection = figma.currentPage.selection;
        if (selection.length === 0) {
            figma.notify('Please select something');
            return;
        }

        console.log('Smart conversion enabled');
        const converted = await Promise.all(selection.map(node => converter.smartConvert(node)));

        console.log('Converted ABC UI Components:', JSON.stringify(converted, null, 2));

        // const prompt = JSON.stringify(converted) + '\n\n根据 figma 截图和 figma 解析出的节点信息及推荐组件，使用 AbcUI 生成对应的 vue sfc 代码，注意布局和排版，figma提供的节点信息供参考生成 dom 结构，suggestedComponentList提供了相关组件的 API，设置属性时，需要严格遵守suggestedComponentList中声明的 props，确保组件使用正确。';
        const prompt = JSON.stringify(converted) + '\n\n根据 figma 解析出的节点信息及推荐组件，使用 AbcUI 生成对应的 vue sfc 代码，figma提供的节点信息供参考生成 dom 结构，注意布局和排版，suggestedComponentList提供了相关组件的 API，设置属性时，需要严格遵守suggestedComponentList中声明的 props，确保组件使用正确。';
        console.log(prompt);

        // 发送 prompt 到 UI 显示
        figma.ui.postMessage({type: 'update-prompt', prompt});
    }
    if (msg.type === 'ui-check') {
        const selection = figma.currentPage.selection;
        if (selection.length === 0) {
            figma.notify('Please select something');
            return;
        }
        console.log('selection', selection, componentConfigMap);

        checkErrors.length = 0;
        checkNode(selection);

        if (checkErrors.length) {
            // 发送 prompt 到 UI 显示
            figma.ui.postMessage({
                type: 'ui-check-fail',
                prompt: '检查不通过' + checkErrors.map(e => `\n${e}`).join('')
            });
        } else {
            figma.ui.postMessage({type: 'ui-check-success', prompt: '检查通过'});
        }

    }

    if (msg.type === 'open-preview') {
        const selection = figma.currentPage.selection;
        if (selection.length === 0) {
            figma.notify('Please select something');
            return;
        }
        // const converted = await Promise.all(selection.map(node => converter.smartConvert(node)));

        // figma.ui.postMessage({type: 'update-prompt', prompt: '生成中...'});
        // const response = await callLLM(generatePrompt(converted));
        //
        // const json = await response.json();
        // console.log('LLM result', json);
        //
        // const content = json?.choices?.[0]?.message?.content;
        // if (content) {
        //     figma.ui.postMessage({type: 'update-prompt', prompt: content});
        // }else{
        //     figma.ui.postMessage({type: 'update-prompt', prompt: '生成失败请重试'});
        // }

        figma.ui.postMessage({type: 'preview-sfc', sfcCode: `<template>  <abc-button    shape="square"    variant="fill"    theme="primary"    size="normal"  >    确定  </abc-button></template><script>export default {}</script>`});
        // figma.ui.postMessage({type: 'open-external-url', url: 'http://127.0.0.1:3000/preview.html'});
    }
};

// Show the UI with specified size
figma.showUI(__html__, {
    width: 600,
    height: 600
});
