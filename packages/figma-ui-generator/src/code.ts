// u4ece ui-transfer u5305u4e2du5bfcu5165u7ec4u4ef6
import {ComponentConverter, nodeToJSON, componentConfigs} from '@abc-figma/ui-transfer';
// import testNode from './test-node.json';
type PluginMessageType =
    'ui-check'
    | 'start-swimming'
    | 'stop-swimming'
    | 'quick-preview'
    | 'inspect-selection'
    | 'convert-to-abc'
    | 'create-shapes'
    | 'cancel'
    | 'copy-to-clipboard'
    | 'create-screenshot'
    | 'set-local-storage'
    | 'get-local-storage'

interface PluginMessage {
    type: PluginMessageType;
    count?: number;
    smart?: boolean;
    text?: string;
    userPrompt?: string;
    llmConfig?: {
        model: 'doubao-seed-1-6-250615' | 'doubao-seed-1-6-flash-250715' | 'doubao-seed-1-6-thinking-250715' | 'deepseek-r1-250528';
        thinking: 'enabled' | 'disabled' | 'auto';
    }
}

console.log('ComponentConverter', ComponentConverter)
// 创建组件转换器实例
const converter = new ComponentConverter();

const globalNodeWhitelist = [
    'title',
    'content',
    'Button',// 包裹按钮的div应该使用abc-space
]
const checkErrors: string[] = [];
// 获取所有组件
const componentConfigMap = Object.values(componentConfigs).reduce((res: Record<string, ComponentConfig>, config: any) => {
    res[config.type] = config;

    if (config.customChildren) {
        Object.entries(config.customChildren).forEach(([key, value]) => {
            res[key] = value;
        })
    }
    return res;
}, {});


// 递归检查节点是否合法
const checkNode = (nodes: readonly SceneNode[], whitelist: string[] = globalNodeWhitelist): void => {

    for (const node of nodes) {
        const nodeWhitelist = componentConfigMap[node.name]?.nodeWhitelist;

        // 排除白名单节点，以及叶子节点一般是文本、icon
        if (!whitelist.includes(node.name) && !componentConfigMap[node.name] && node.children?.length) {
            checkErrors.push(node.name);
        }
        if (node.children?.length) {
            checkNode(node.children, globalNodeWhitelist.concat(nodeWhitelist || []));
        }
    }
};

function generatePrompt(figmaInfo: string) {
    return `根据 figma 解析出的节点信息及推荐组件，使用 AbcUI 生成完整的 vue 单文件组件代码，
                    \n【重要】返回结果只包含 vue 单文件组件代码的纯文本字符串，组件名均使用kebab-case，不要有语法错误，不要返回其他任何无关信息，不要代码块语法包裹内容，不要带任何转义符、特殊符号、emoji等。
                    - 不要使用import导入任何AbcUI组件或者Biz开头的业务组件，直接使用组件即可，无需注册。
                    - 使用figma提供的节点信息参考生成 dom 结构，注意布局和排版。
                    - suggestedComponentList提供了相关组件的 API，设置属性时，需要严格遵守suggestedComponentList中声明的 props，确保组件使用正确。
                    - 如果生成的代码有AbcDialog，要保证AbcDialog设置了 v-model，并且初始变量是true, 并且对底部取消或关闭按钮设置关闭弹窗事件。
                    \n
                    转换规则：
                    1. 如果 componentProperties 的值是字符串（例如 "提交", "blue"），必须直接写成字符串字面量，不要加 : 号，例如 label="提交"。
                    2. 如果 props 的值是布尔、数字或表达式，才使用 : 绑定，例如 :disabled="true", :count="5"。
                    \n${JSON.stringify(figmaInfo)}`
}

// 处理来自 UI 的消息
figma.ui.onmessage = async (msg: PluginMessage) => {
    if (msg.type === 'create-shapes') {
        const nodes: SceneNode[] = [];
        for (let i = 0; i < msg.count!; i++) {
            const rect = figma.createRectangle();
            rect.x = i * 150;
            rect.fills = [{type: 'SOLID', color: {r: 1, g: 0.5, b: 0}}];
            figma.currentPage.appendChild(rect);
            nodes.push(rect);
        }
        figma.currentPage.selection = nodes;
        figma.viewport.scrollAndZoomIntoView(nodes);
    }

    if (msg.type === 'cancel') {
        figma.closePlugin();
    }

    if (msg.type === 'set-local-storage') {
        // 设置本地存储的模型配置
        figma.clientStorage.setAsync(msg.data.key, msg.data.value)
            .then(() => {
                console.log('本地存储设置成功');
            })
            .catch(error => {
                console.error('本地存储设置失败:', error);
            });
    }

    if (msg.type === 'get-local-storage') {
        // 获取本地存储的模型配置
        figma.clientStorage.getAsync(msg.data.key)
            .then(value => {
                // 如果存在值则返回，否则返回默认值
                const result = value || msg.data.defaultValue;
                figma.ui.postMessage({
                    type: 'figma-storage',
                    data: {
                        key: msg.data.key,
                        value: result
                    }
                });
            })
            .catch(error => {
                console.error('获取本地存储失败:', error);
                // 获取失败时返回默认值
                figma.ui.postMessage({
                    type: 'figma-storage',
                    data: {
                        key: msg.data.key,
                        value: msg.data.defaultValue
                    }
                });
            });
    }

    if (msg.type === 'inspect-selection') {
        const selection = figma.currentPage.selection;
        if (selection.length === 0) {
            figma.notify('Please select something');
            return;
        }

        const json = selection.map(node => nodeToJSON(node));
        console.log('Selected Elements:', JSON.stringify(json, null, 2));
        figma.notify('Selection info logged to console');

        // console.log('testNode', testNode.nodes['11446:60174'].document);
        // const result = await converter.smartConvert(testNode.nodes['11446:60174'].document);
        // console.log('testNode result', JSON.stringify(result, null, 2));
    }

    if (msg.type === 'create-screenshot') {
        const selection = figma.currentPage.selection;
        if (selection.length === 0) {
            figma.notify('请先选择需要截图的节点');
            return;
        }

        // 获取选中节点的边界框
        const node = selection[0];
        const imageData = await node.exportAsync({
            format: 'PNG',
            constraint: {type: 'SCALE', value: 2}
        });

        // 将 Uint8Array 转换为 Base64
        const base64Data = figma.base64Encode(imageData);

        // 发送 Base64 数据到 UI 层
        figma.ui.postMessage({
            type: "copy-to-clipboard",
            data: `data:image/png;base64,${base64Data}`,
        });

        figma.notify('截图已生成，请查看下载文件夹');
    }

    if (msg.type === 'convert-to-abc') {
        const selection = figma.currentPage.selection;
        if (selection.length === 0) {
            figma.notify('Please select something');
            return;
        }

        const converted = await Promise.all(selection.map(node => converter.smartConvert(node)));

        const prompt = generatePrompt(converted);

        // 发送 prompt 到 UI 显示
        figma.ui.postMessage({type: 'update-prompt', prompt});
    }
    if (msg.type === 'ui-check') {
        const selection = figma.currentPage.selection;
        if (selection.length === 0) {
            figma.notify('Please select something');
            return;
        }
        console.log('selection', selection, componentConfigMap);

        checkErrors.length = 0;
        checkNode(selection);

        if (checkErrors.length) {
            // 发送 prompt 到 UI 显示
            figma.ui.postMessage({
                type: 'update-prompt',
                prompt: '检查不通过' + checkErrors.map(e => `\n${e}`).join('')
            });
        } else {
            figma.ui.postMessage({type: 'update-prompt', prompt: '检查通过'});
        }

    }

    if (msg.type === 'quick-preview') {
        const selection = figma.currentPage.selection;
        if (selection.length === 0) {
            figma.notify('Please select something');
            return;
        }
        const converted = await Promise.all(selection.map(node => converter.smartConvert(node)));

        const prompt = generatePrompt(converted);

        // 回显提示词
        figma.ui.postMessage({
            type: 'update-prompt',
            prompt,
            // 标记需要开始请求
            isStartSwimming: true,
        });
    }
};

// Show the UI with specified size
figma.showUI(__html__, {
    width: 600,
    height: 800
});
