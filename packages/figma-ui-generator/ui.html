<!DOCTYPE html>
<html lang="en">

<head>
<!--    <style>-->
<!--        body {-->
<!--            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;-->
<!--            margin: 0;-->
<!--            padding: 20px;-->
<!--            width: 100%;-->
<!--            height: 100%;-->
<!--            box-sizing: border-box;-->
<!--        }-->

<!--        .container {-->
<!--            display: flex;-->
<!--            flex-direction: column;-->
<!--            height: 100%;-->
<!--            gap: 20px;-->
<!--        }-->

<!--        .buttons {-->
<!--            display: flex;-->
<!--            flex-direction: row;-->
<!--            gap: 12px;-->
<!--        }-->

<!--        button {-->
<!--            flex: 1;-->
<!--            max-width: 200px;-->
<!--            padding: 12px;-->
<!--            border: none;-->
<!--            border-radius: 6px;-->
<!--            background-color: #18a0fb;-->
<!--            color: white;-->
<!--            cursor: pointer;-->
<!--            transition: all 0.2s;-->
<!--            font-size: 14px;-->
<!--            font-weight: 500;-->
<!--            white-space: nowrap;-->
<!--        }-->

<!--        button:not(.copy-button):hover:not(:disabled) {-->
<!--            background-color: #0d8ee9;-->
<!--        }-->

<!--        .copy-button {-->
<!--            background-color: white;-->
<!--            color: #333;-->
<!--            border: 1px solid #e0e0e0;-->
<!--        }-->

<!--        .copy-button:hover:not(:disabled) {-->
<!--            background-color: #f5f5f5;-->
<!--            border-color: #d0d0d0;-->
<!--        }-->

<!--        button:disabled {-->
<!--            background-color: #e0e0e0;-->
<!--            color: #999;-->
<!--            cursor: not-allowed;-->
<!--            border: none;-->
<!--        }-->

<!--        .prompt-container {-->
<!--            flex-grow: 1;-->
<!--            display: flex;-->
<!--            flex-direction: column;-->
<!--        }-->

<!--        .prompt-header {-->
<!--            display: flex;-->
<!--            align-items: center;-->
<!--            justify-content: space-between;-->
<!--            margin-bottom: 12px;-->
<!--        }-->

<!--        .prompt-header h3 {-->
<!--            margin: 0;-->
<!--            color: #333;-->
<!--            font-size: 14px;-->
<!--            font-weight: 500;-->
<!--        }-->

<!--        .copy-button {-->
<!--            padding: 6px 12px;-->
<!--            font-size: 12px;-->
<!--            border: 1px solid #e0e0e0;-->
<!--            border-radius: 4px;-->
<!--            background-color: white;-->
<!--            color: #333;-->
<!--            cursor: pointer;-->
<!--            transition: all 0.2s;-->
<!--        }-->

<!--        .copy-button:hover {-->
<!--            background-color: #f5f5f5;-->
<!--            border-color: #d0d0d0;-->
<!--        }-->

<!--        .copy-button.copied {-->
<!--            background-color: #e8f5e9;-->
<!--            border-color: #4caf50;-->
<!--            color: #2e7d32;-->
<!--        }-->

<!--        #ui-check.success {-->
<!--            background-color: #4caf50;-->
<!--            border-color: #2e7d32;-->
<!--            color: #e8f5e9;-->
<!--        }-->

<!--        #ui-check.fail {-->
<!--            background-color: #ff99b3;-->
<!--            border-color: #ff3366;-->
<!--            color: #e8f5e9;-->
<!--        }-->

<!--        .prompt-content {-->
<!--            flex-grow: 1;-->
<!--            position: relative;-->
<!--            min-height: 0;-->
<!--        }-->

<!--        #prompt-display {-->
<!--            position: absolute;-->
<!--            top: 0;-->
<!--            left: 0;-->
<!--            right: 0;-->
<!--            bottom: 0;-->
<!--            padding: 16px;-->
<!--            border: 1px solid #e0e0e0;-->
<!--            border-radius: 6px;-->
<!--            background-color: #f8f9fa;-->
<!--            font-family: 'SF Mono', Monaco, Menlo, Consolas, monospace;-->
<!--            font-size: 13px;-->
<!--            line-height: 1.5;-->
<!--            color: #333;-->
<!--            overflow-y: auto;-->
<!--            white-space: pre-wrap;-->
<!--        }-->
<!--    </style>-->
</head>

<body>
    <div id="app"></div>

    <div class="container">
            <div class="buttons">
                <button id="preview" class="abc-button abc-button--fill abc-button--primary abc-button--square abc-button-primary abc-button-normal">预览</button>
            <button id="ui-check">UI检查</button>
            <button id="convert-smart">Prompt</button>
            <button class="copy-button" id="copy-prompt" disabled>复制</button>
            <!-- <button id="screenshot">截图</button> -->
            <button id="inspect">Debug</button>
        </div>

        <div class="prompt-container">
            <div class="prompt-header">
                <h3>Generated Prompt</h3>
            </div>
            <div class="prompt-content">
                <div id="prompt-display"></div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.7.14/dist/vue.min.js"></script>
    <!-- 关键修正：使用正确的编译器链接和全局变量 -->
    <script src="https://cdn.jsdelivr.net/npm/vue-template-compiler@2.7.14/browser.js"></script>
    <script src="https://unpkg.com/@babel/standalone@7.22.15/babel.min.js"></script>
    <script src="https://static-common-cdn.abcyun.cn/iconfont/pc/font_4448419_ik71xl7tokr/iconfont.js"></script>
    <script>
        // 全局注册组件库
        // console.log(123123,Vue, UIPc)
        // if(Vue && UIPc) {
        //     Vue.use(UIPc.default);
        //     UIPc.setAbcTheme('pharmacy')
        // }

        let currentVueInstance = null;

        document.getElementById('preview').onclick = () => {
            parent.postMessage({pluginMessage: {type: 'open-preview'}}, '*')
        }

        document.getElementById('ui-check').onclick = () => {
            parent.postMessage({pluginMessage: {type: 'ui-check', smart: true}}, '*')
        }

        document.getElementById('inspect').onclick = () => {
            parent.postMessage({pluginMessage: {type: 'inspect-selection'}}, '*')
        }

        document.getElementById('convert-smart').onclick = () => {
            parent.postMessage({pluginMessage: {type: 'convert-to-abc', smart: true}}, '*')
        }

        document.getElementById('copy-prompt').onclick = () => {
            const promptDisplay = document.getElementById('prompt-display');
            const copyButton = document.getElementById('copy-prompt');

            // 创建一个临时的 input 元素
            const input = document.createElement('input');
            input.value = promptDisplay.textContent;
            input.style.position = 'absolute';
            input.style.left = '-9999px';
            document.body.appendChild(input);

            // 选中并复制
            input.focus();
            input.select();

            try {
                // 尝试复制
                const successful = document.execCommand('copy');
                if (successful) {
                    copyButton.textContent = '已复制';
                    copyButton.classList.add('copied');
                } else {
                    copyButton.textContent = '复制失败';
                }
            } catch (err) {
                console.error('复制失败:', err);
                copyButton.textContent = '复制失败';
            }

            // 移除临时元素
            document.body.removeChild(input);

            // 2秒后恢复按钮状态
            setTimeout(() => {
                copyButton.textContent = '复制';
                copyButton.classList.remove('copied');
            }, 2000);
        }

        // 核心更新逻辑
        async function updatePreview(sfcCode) {
            const Vue = window.Vue;
            const VueTemplateCompiler = window.VueTemplateCompiler;
            const Babel = window.Babel;

            try {
                // 解析SFC组件
                const res = VueTemplateCompiler.parseComponent(sfcCode);
                const template = res.template.content;
                const script = res.script.content;

                // 编译模板
                const {render} = VueTemplateCompiler.compile(template, {
                    preserveWhitespace: false
                });

                // 处理Script部分
                const scriptContent = script
                    .replace(/export\s+default/, 'var __script = ')
                    .replace(/import\s+.*?\s+from\s+['"].*?['"];?/g, '');

                // Babel转换
                const {code} = Babel.transform(scriptContent, {
                    presets: ['es2015'],
                    plugins: ['transform-modules-commonjs']
                });

                // 执行脚本获取配置
                const scriptResult = new Function(`
                    ${code}
                    return typeof __script === 'undefined' ? {} : __script;
                `)();

                // 合并组件选项
                const componentOptions = {
                    ...scriptResult,
                    render: new Function(render)
                };

                // 清理旧实例
                if (currentVueInstance) {
                    currentVueInstance.$destroy();
                    currentVueInstance = null;
                }

                // 创建新实例
                currentVueInstance = new Vue(componentOptions);
                const previewContainer = document.getElementById('prompt-display');
                previewContainer.innerHTML = '';
                currentVueInstance.$mount();
                previewContainer.appendChild(currentVueInstance.$el);

            } catch (error) {
                console.error('编译错误:', error);
                throw error;
            }
        }

        // 监听来自插件的消息
        window.onmessage = async (event) => {
            const msg = event.data.pluginMessage;
            if (msg) {
                if (msg.type === 'copy-to-clipboard') {
                    try {
                        // 创建一个临时的 img 元素
                        const img = document.createElement('img');
                        img.src = msg.data;

                        // 等待图片加载完成
                        img.onload = function () {
                            // 创建一个临时的 canvas 元素
                            const canvas = document.createElement('canvas');
                            canvas.width = img.width;
                            canvas.height = img.height;

                            // 将图片绘制到 canvas
                            const ctx = canvas.getContext('2d');
                            ctx.drawImage(img, 0, 0);

                            try {
                                // 将 canvas 转换为 blob URL
                                canvas.toBlob(function (blob) {
                                    // 创建一个临时的 a 元素
                                    const url = URL.createObjectURL(blob);
                                    const link = document.createElement('a');
                                    link.href = url;
                                    link.download = 'screenshot.png';
                                    document.body.appendChild(link);
                                    link.click();

                                    // 清理
                                    document.body.removeChild(link);
                                    URL.revokeObjectURL(url);
                                }, 'image/png');

                                console.log('截图已保存！');
                            } catch (err) {
                                console.error('保存失败:', err);
                            }
                        };
                    } catch (error) {
                        console.error('复制失败：', error);
                    }
                } else if (msg.type === 'update-prompt') {
                    const promptDisplay = document.getElementById('prompt-display');
                    const copyButton = document.getElementById('copy-prompt');
                    promptDisplay.textContent = msg.prompt;
                    // 启用复制按钮
                    copyButton.disabled = false;
                } else if (msg.type === 'ui-check-success') {
                    const promptDisplay = document.getElementById('prompt-display');
                    const checkButton = document.getElementById('ui-check');
                    promptDisplay.textContent = msg.prompt;
                    // 添加成功样式
                    checkButton.classList.remove('fail', 'success');
                    checkButton.classList.add('success');
                } else if (msg.type === 'ui-check-fail') {
                    const promptDisplay = document.getElementById('prompt-display');
                    const checkButton = document.getElementById('ui-check');
                    promptDisplay.textContent = msg.prompt;
                    // 添加失败样式
                    checkButton.classList.remove('fail', 'success');
                    checkButton.classList.add('fail');
                } else if (msg.type === 'open-external-url') {
                    // 在新窗口中打开链接
                    window.open(msg.url, '_blank');
                } else if (msg.type === 'preview-sfc') {
                    // 在当前预览代码
                    // updatePreview(msg.sfcCode);
                    console.log('msg.sfcCode', msg.sfcCode)
                }
            }
        }
    </script>
</body>
</html>