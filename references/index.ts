import { pascalToKebabCase } from "@/utils/utils";

// 使用 webpack require.context 动态导入所有配置文件
const context = require.context('./abc-ui', false, /\.json$/);

// 动态导入所有配置
const componentDocs: Record<string, any> = {};

// 遍历所有配置文件
context.keys().forEach((key: string) => {
    // 排除 .new.config.ts 文件
    const componentDoc = context(key);
    const originName = componentDoc.name;
    let resolvedName = originName;
    // if (originName.includes('V2') && !originName.includes('AbcTabsV2')) {
    //     resolvedName = originName.replace('V2', '');
    // }
    // 将例如 AbcTree 转成 abc-tree
    let kebabName = pascalToKebabCase(resolvedName);

    componentDocs[resolvedName] = {
        ...componentDoc,
        resolvedName,
        kebabName
    };
});

// 默认组件映射
export const defaultComponentMapping: Record<string, string> = {
    'TEXT': 'AbcText',
    // 'FRAME': 'AbcFlex',
    // 'GROUP': 'AbcFlex',
    // 'RECTANGLE': 'AbcCard',
    // 'INSTANCE': 'AbcFlex',
    // 'COMPONENT': 'AbcFlex'
};

export { componentDocs };
